#!/usr/bin/env python3
"""
测试stage1生成器测试功能
"""

import torch
import sys
import os

def test_stage1_generator_function():
    """测试stage1生成器测试功能"""
    print("🔍 测试stage1生成器测试功能...")
    
    try:
        # 导入必要的模块
        from train2 import (
            Config, DecompositionalEncoder, ReconstructionDecoder,
            PCFDiscriminator, Normalize, test_generator_stage1,
            calculate_reconstruction_errors, create_generator_comparison_plot
        )
        from clpf_generator import NewCLPFGenerator
        
        # 创建配置
        cfg = Config()
        device = torch.device("cpu")  # 使用CPU避免GPU问题
        
        print("  - 创建模型组件...")
        
        # 创建模型
        processor = DecompositionalEncoder(cfg).to(device)
        autoencoder = ReconstructionDecoder(cfg).to(device)
        clpf_generator = NewCLPFGenerator(cfg.latent_dim, cfg, device).to(device)
        d_season = PCFDiscriminator(cfg).to(device)
        d_trend = PCFDiscriminator(cfg).to(device)
        denormalizer = Normalize(cfg.enc_in, affine=True).to(device)
        
        models = (processor, autoencoder, clpf_generator, d_season, d_trend, denormalizer)
        
        print("  - 测试生成器测试函数...")
        
        # 测试生成器测试函数
        test_generator_stage1(cfg, models, device)
        
        print("✅ stage1生成器测试功能正常！")
        return True
        
    except Exception as e:
        print(f"❌ stage1生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_functions():
    """测试各个独立函数"""
    print("\n🔍 测试各个独立函数...")
    
    try:
        from train2 import calculate_reconstruction_errors, create_generator_comparison_plot
        
        # 创建虚拟数据
        batch_size = 2
        seq_len = 12
        d_model = 64
        num_scales = 3
        
        # 创建虚拟的多尺度数据
        fake_season_list = []
        fake_trend_list = []
        redecomposed_season = []
        redecomposed_trend = []
        
        for scale in range(num_scales):
            scale_seq_len = seq_len // (2 ** scale)
            fake_season_list.append(torch.randn(batch_size, scale_seq_len, d_model))
            fake_trend_list.append(torch.randn(batch_size, scale_seq_len, d_model))
            redecomposed_season.append(torch.randn(batch_size, scale_seq_len, d_model))
            redecomposed_trend.append(torch.randn(batch_size, scale_seq_len, d_model))
        
        reconstructed_norm = torch.randn(batch_size, seq_len, d_model)
        
        print("  - 测试误差计算函数...")
        calculate_reconstruction_errors(
            fake_season_list, fake_trend_list,
            redecomposed_season, redecomposed_trend
        )
        
        print("  - 测试可视化函数...")
        create_generator_comparison_plot(
            fake_season_list, fake_trend_list,
            redecomposed_season, redecomposed_trend,
            reconstructed_norm
        )
        
        print("✅ 各个独立函数测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 独立函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Stage1生成器测试功能验证")
    print("=" * 50)
    
    # 设置随机种子
    torch.manual_seed(42)
    
    tests = [
        ("独立函数测试", test_individual_functions),
        ("完整功能测试", test_stage1_generator_function),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"⚠️  {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！stage1生成器测试功能已成功添加！")
        print("\n📋 使用说明:")
        print("1. 在train2.py中运行stage1训练后，会自动调用生成器测试")
        print("2. 测试结果会保存在 stage1_test_results/ 目录下")
        print("3. 包含详细的误差分析和可视化对比图")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
