#!/usr/bin/env python3
"""
调试导入问题的脚本
"""

import sys
import traceback

def test_step_by_step():
    """逐步测试每个导入"""
    print("🔍 逐步导入测试")
    print("=" * 40)
    
    # 1. 测试基础torch
    try:
        import torch
        print("✅ torch 导入成功")
    except Exception as e:
        print(f"❌ torch 导入失败: {e}")
        return False
    
    # 2. 测试clpf_lib基础
    try:
        import clpf_lib
        print("✅ clpf_lib 包导入成功")
    except Exception as e:
        print(f"❌ clpf_lib 包导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 3. 测试clpf_lib.utils
    try:
        from clpf_lib import utils
        print("✅ clpf_lib.utils 导入成功")
    except Exception as e:
        print(f"❌ clpf_lib.utils 导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 4. 测试clpf_lib.layers
    try:
        from clpf_lib import layers
        print("✅ clpf_lib.layers 导入成功")
    except Exception as e:
        print(f"❌ clpf_lib.layers 导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 5. 测试clpf_tools
    try:
        import clpf_tools
        print("✅ clpf_tools 导入成功")
    except Exception as e:
        print(f"❌ clpf_tools 导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 6. 测试clpf_generator
    try:
        import clpf_generator
        print("✅ clpf_generator 导入成功")
    except Exception as e:
        print(f"❌ clpf_generator 导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 7. 测试NewCLPFGenerator类
    try:
        from clpf_generator import NewCLPFGenerator
        print("✅ NewCLPFGenerator 类导入成功")
    except Exception as e:
        print(f"❌ NewCLPFGenerator 类导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 8. 测试train2基础导入
    try:
        import train2
        print("✅ train2 模块导入成功")
    except Exception as e:
        print(f"❌ train2 模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n🎉 所有导入测试通过！")
    return True

def test_specific_layers():
    """测试具体的层导入"""
    print("\n🔍 测试具体层导入")
    print("=" * 40)
    
    layer_modules = [
        'cnf',
        'container', 
        'odefunc',
        'odefunc_aug',
        'squeeze'
    ]
    
    for module_name in layer_modules:
        try:
            module = __import__(f'clpf_lib.layers.{module_name}', fromlist=[module_name])
            print(f"✅ clpf_lib.layers.{module_name} 导入成功")
        except Exception as e:
            print(f"❌ clpf_lib.layers.{module_name} 导入失败: {e}")
            traceback.print_exc()
            return False
    
    return True

def test_clpf_dependencies():
    """测试CLPF的依赖"""
    print("\n🔍 测试CLPF依赖")
    print("=" * 40)
    
    dependencies = [
        'torchdiffeq',
        'six',
        'numpy',
        'matplotlib'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 可用")
        except ImportError:
            print(f"⚠️  {dep} 不可用 (可能需要安装)")
        except Exception as e:
            print(f"❌ {dep} 导入错误: {e}")

def main():
    """主函数"""
    print("🚀 CLPF导入调试工具")
    print("=" * 50)
    
    # 显示Python环境信息
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {sys.path[0]}")
    
    # 运行测试
    success = True
    
    if not test_step_by_step():
        success = False
    
    if success and not test_specific_layers():
        success = False
    
    test_clpf_dependencies()  # 这个不影响成功状态
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 导入调试完成，没有发现问题！")
        print("可以继续运行: python quick_test.py")
    else:
        print("❌ 发现导入问题，请根据错误信息修复")
    
    return success

if __name__ == "__main__":
    main()
