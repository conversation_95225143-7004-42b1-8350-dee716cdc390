# New CLPF Generator for TimeMixerGAN
# This generator uses the CLPF model to generate trend and seasonal components

import torch
import torch.nn as nn
from typing import List, Tuple
import numpy as np

from clpf_lib import layers
from clpf_lib.utils import sample_standard_gaussian
from clpf_tools import build_augmented_model_tabular
from clpf_ode_rnn_encoder import create_ode_rnn_encoder
from clpf_train_misc import create_regularization_fns, set_cnf_options


class CLPFArgs:
    """Arguments for CLPF model"""
    def __init__(self):
        # Basic settings
        self.dims = "32,64,64,32"
        self.aug_hidden_dims = None
        self.aug_dim = 0
        self.num_blocks = 1
        self.encoder = "ode_rnn"
        self.conv = False
        self.layer_type = "concat"
        self.divergence_fn = "approximate"
        self.nonlinearity = "tanh"
        self.solver = "dopri5"
        self.atol = 1e-5
        self.rtol = 1e-5
        self.step_size = None
        self.test_solver = None
        self.test_atol = None
        self.test_rtol = None
        
        # Model dimensions
        self.input_size = 1
        self.aug_size = 1
        self.latent_size = 10
        self.rec_size = 20
        self.rec_layers = 1
        self.units = 100
        self.gru_units = 100
        self.num_iwae_samples = 3
        self.niwae_test = 25
        
        # Training settings
        self.alpha = 1e-6
        self.time_length = 1.0
        self.train_T = True
        self.aug_mapping = True
        self.activation = "identity"
        self.batch_norm = False
        self.residual = False
        self.autoencode = False
        self.rademacher = True
        self.multiscale = False
        self.parallel = False
        
        # Regularizations
        self.l1int = None
        self.l2int = None
        self.dl2int = None
        self.JFrobint = None
        self.JdiagFrobint = None
        self.JoffdiagFrobint = None
        self.time_penalty = 0
        self.max_grad_norm = 1e10
        
        # Optimizer settings
        self.lr = 1e-3
        self.optimizer = "adam"
        self.amsgrad = False
        self.momentum = 0.9
        self.weight_decay = 0.0
        
        # Set effective shape
        self.effective_shape = self.input_size


class NewCLPFGenerator(nn.Module):
    """
    New CLPF Generator that outputs trend and seasonal components
    Input: random noise
    Output: trend and seasonal sequences for different scales
    """
    
    def __init__(self, latent_dim: int, config, device=torch.device("cpu")):
        super(NewCLPFGenerator, self).__init__()
        self.config = config
        self.device = device
        self.latent_dim = latent_dim
        
        # Create CLPF arguments
        self.clpf_args = CLPFArgs()
        self.clpf_args.latent_size = latent_dim
        
        # Calculate output dimensions for different scales
        self.output_dims = []
        tm_params = config.timemixer_params
        for i in range(tm_params['down_sampling_layers'] + 1):
            seq_len_i = config.seq_len // (tm_params['down_sampling_window'] ** i)
            self.output_dims.append(seq_len_i * tm_params['d_model'])
        
        # Build CLPF model components
        regularization_fns, regularization_coeffs = create_regularization_fns(self.clpf_args)
        
        # Create encoder for latent CLPF
        self.encoder = create_ode_rnn_encoder(self.clpf_args, device)
        
        # Create augmented model (decoder)
        total_dim = self.clpf_args.aug_size + self.clpf_args.effective_shape + self.clpf_args.latent_size
        self.aug_model = build_augmented_model_tabular(
            self.clpf_args,
            total_dim,
            regularization_fns=regularization_fns,
        )
        
        set_cnf_options(self.clpf_args, self.aug_model)
        
        # Projection layers to map CLPF output to TimeMixer format
        self.trend_projections = nn.ModuleList()
        self.seasonal_projections = nn.ModuleList()
        
        for i, output_dim in enumerate(self.output_dims):
            seq_len_i = config.seq_len // (tm_params['down_sampling_window'] ** i)
            # Project from latent space to trend/seasonal components
            self.trend_projections.append(
                nn.Sequential(
                    nn.Linear(self.clpf_args.latent_size, output_dim),
                    nn.Tanh(),
                    nn.Linear(output_dim, seq_len_i * tm_params['d_model'])
                )
            )
            self.seasonal_projections.append(
                nn.Sequential(
                    nn.Linear(self.clpf_args.latent_size, output_dim),
                    nn.Tanh(),
                    nn.Linear(output_dim, seq_len_i * tm_params['d_model'])
                )
            )
    
    def forward(self, z: torch.Tensor) -> Tuple[List[torch.Tensor], List[torch.Tensor]]:
        """
        Forward pass of CLPF generator
        
        Args:
            z: Random noise tensor of shape (batch_size, latent_dim)
            
        Returns:
            Tuple of (seasonal_list, trend_list) where each list contains tensors
            for different scales
        """
        batch_size = z.size(0)
        
        # Sample from latent space using CLPF
        # For simplicity, we'll use the input noise directly as latent variables
        # In a full implementation, this would involve the encoder-decoder process
        
        # Generate latent representations
        mu = torch.zeros_like(z)
        sigma = torch.ones_like(z)
        latent_samples = sample_standard_gaussian(mu, sigma)
        
        # Project latent samples to trend and seasonal components
        seasonal_list = []
        trend_list = []
        
        tm_params = self.config.timemixer_params
        
        for i, (trend_proj, seasonal_proj) in enumerate(zip(self.trend_projections, self.seasonal_projections)):
            seq_len_i = self.config.seq_len // (tm_params['down_sampling_window'] ** i)
            
            # Generate trend and seasonal components
            trend_flat = trend_proj(latent_samples)
            seasonal_flat = seasonal_proj(latent_samples)
            
            # Reshape to proper dimensions
            trend_component = trend_flat.view(batch_size, seq_len_i, tm_params['d_model'])
            seasonal_component = seasonal_flat.view(batch_size, seq_len_i, tm_params['d_model'])
            
            trend_list.append(trend_component)
            seasonal_list.append(seasonal_component)
        
        return seasonal_list, trend_list
    
    def generate_from_noise(self, z: torch.Tensor) -> Tuple[List[torch.Tensor], List[torch.Tensor]]:
        """
        Generate trend and seasonal components from noise
        This is the main interface for the generator
        """
        return self.forward(z)
