#!/usr/bin/env python3
"""
测试脚本：验证train2.py中CLPF模型移植的功能
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from typing import List, Tuple

def test_imports():
    """测试所有必要的导入"""
    print("=" * 50)
    print("测试1: 导入测试")
    print("=" * 50)
    
    try:
        # 测试CLPF相关导入
        from clpf_lib import layers
        print("✅ clpf_lib.layers 导入成功")
        
        from clpf_lib.utils import sample_standard_gaussian
        print("✅ clpf_lib.utils 导入成功")
        
        from clpf_tools import build_augmented_model_tabular
        print("✅ clpf_tools 导入成功")
        
        from clpf_generator import NewCLPFGenerator
        print("✅ NewCLPFGenerator 导入成功")
        
        # 测试train2.py的其他导入
        from data_loader import load_data
        print("✅ data_loader 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_clpf_generator():
    """测试CLPF生成器"""
    print("\n" + "=" * 50)
    print("测试2: CLPF生成器测试")
    print("=" * 50)
    
    try:
        from clpf_generator import NewCLPFGenerator
        
        # 创建测试配置
        class TestConfig:
            def __init__(self):
                self.seq_len = 24
                self.latent_dim = 128
                self.timemixer_params = {
                    'd_model': 768, 
                    'd_ff': 3072, 
                    'dropout': 0.1,
                    'down_sampling_window': 2, 
                    'down_sampling_layers': 3, 
                    'moving_avg': 9,
                    'enc_in': 5,
                    'c_out': 5
                }
        
        config = TestConfig()
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        # 创建CLPF生成器
        generator = NewCLPFGenerator(config.latent_dim, config, device)
        print("✅ CLPF生成器创建成功")
        
        # 测试前向传播
        batch_size = 4
        z = torch.randn(batch_size, config.latent_dim, device=device)
        
        with torch.no_grad():
            seasonal_list, trend_list = generator(z)
        
        print(f"✅ 前向传播成功")
        print(f"   - 季节性组件数量: {len(seasonal_list)}")
        print(f"   - 趋势组件数量: {len(trend_list)}")
        
        # 验证输出形状
        for i, (s, t) in enumerate(zip(seasonal_list, trend_list)):
            expected_seq_len = config.seq_len // (config.timemixer_params['down_sampling_window'] ** i)
            expected_shape = (batch_size, expected_seq_len, config.timemixer_params['d_model'])
            
            assert s.shape == expected_shape, f"季节性组件{i}形状错误: {s.shape} vs {expected_shape}"
            assert t.shape == expected_shape, f"趋势组件{i}形状错误: {t.shape} vs {expected_shape}"
            
            print(f"   - 尺度{i}: {s.shape}")
        
        print("✅ 输出形状验证通过")
        return True
        
    except Exception as e:
        print(f"❌ CLPF生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_components():
    """测试train2.py中的模型组件"""
    print("\n" + "=" * 50)
    print("测试3: 模型组件测试")
    print("=" * 50)
    
    try:
        # 导入train2.py中的组件
        sys.path.append('.')
        from train2 import (
            Config, DecompositionalEncoder, ReconstructionDecoder, 
            PCFDiscriminator, Normalize
        )
        from clpf_generator import NewCLPFGenerator
        
        # 创建配置
        cfg = Config()
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 测试各个组件
        print("创建模型组件...")
        
        processor = DecompositionalEncoder(cfg).to(device)
        print("✅ DecompositionalEncoder 创建成功")
        
        autoencoder = ReconstructionDecoder(cfg).to(device)
        print("✅ ReconstructionDecoder 创建成功")
        
        clpf_generator = NewCLPFGenerator(cfg.latent_dim, cfg, device).to(device)
        print("✅ NewCLPFGenerator 创建成功")
        
        d_season = PCFDiscriminator(cfg).to(device)
        print("✅ PCFDiscriminator (season) 创建成功")
        
        d_trend = PCFDiscriminator(cfg).to(device)
        print("✅ PCFDiscriminator (trend) 创建成功")
        
        # 测试数据流
        batch_size = 2
        test_input = torch.randn(batch_size, cfg.seq_len, cfg.enc_in, device=device)
        
        print("\n测试数据流...")
        
        # 测试处理器
        with torch.no_grad():
            real_season, real_trend, mean, stdev = processor(test_input)
            print(f"✅ 处理器输出: {len(real_season)} 个季节性组件, {len(real_trend)} 个趋势组件")
        
        # 测试生成器
        z = torch.randn(batch_size, cfg.latent_dim, device=device)
        with torch.no_grad():
            fake_season, fake_trend = clpf_generator(z)
            print(f"✅ 生成器输出: {len(fake_season)} 个季节性组件, {len(fake_trend)} 个趋势组件")
        
        # 测试自编码器
        with torch.no_grad():
            reconstructed = autoencoder(fake_season, fake_trend)
            print(f"✅ 自编码器输出形状: {reconstructed.shape}")
        
        # 测试判别器
        with torch.no_grad():
            d_season_real = d_season(real_season)
            d_season_fake = d_season(fake_season)
            print(f"✅ 季节性判别器输出: 真实 {d_season_real.shape}, 虚假 {d_season_fake.shape}")
            
            d_trend_real = d_trend(real_trend)
            d_trend_fake = d_trend(fake_trend)
            print(f"✅ 趋势判别器输出: 真实 {d_trend_real.shape}, 虚假 {d_trend_fake.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_functions():
    """测试训练函数"""
    print("\n" + "=" * 50)
    print("测试4: 训练函数测试")
    print("=" * 50)
    
    try:
        from train2 import (
            Config, stage1_train_generators, stage2_train_discriminators,
            stage3_joint_train, DecompositionalEncoder, ReconstructionDecoder,
            PCFDiscriminator, Normalize
        )
        from clpf_generator import NewCLPFGenerator
        import torch.optim as optim
        
        # 创建小规模测试配置
        cfg = Config()
        cfg.seq_len = 12  # 减小序列长度以加快测试
        cfg.batch_size = 2
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建模型
        processor = DecompositionalEncoder(cfg).to(device)
        autoencoder = ReconstructionDecoder(cfg).to(device)
        clpf_generator = NewCLPFGenerator(cfg.latent_dim, cfg, device).to(device)
        d_season = PCFDiscriminator(cfg).to(device)
        d_trend = PCFDiscriminator(cfg).to(device)
        denormalizer = Normalize(cfg.enc_in, affine=True).to(device)
        
        models = (processor, autoencoder, clpf_generator, d_season, d_trend, denormalizer)
        
        # 创建优化器
        optimizer_G = optim.Adam(list(clpf_generator.parameters()), lr=cfg.lr_g, betas=cfg.betas)
        optimizer_D = optim.Adam(list(d_season.parameters()) + list(d_trend.parameters()), lr=cfg.lr_d, betas=cfg.betas)
        optimizer_G_PA = optim.Adam(list(clpf_generator.parameters()) + list(processor.parameters()) + list(autoencoder.parameters()), lr=cfg.lr_g, betas=cfg.betas)
        
        optimizers = {
            'G': optimizer_G,
            'D': optimizer_D,
            'G_PA': optimizer_G_PA
        }
        
        # 创建虚拟数据加载器
        class DummyDataLoader:
            def __init__(self, batch_size, seq_len, enc_in, num_batches=2):
                self.batch_size = batch_size
                self.seq_len = seq_len
                self.enc_in = enc_in
                self.num_batches = num_batches
                self.current_batch = 0
            
            def __iter__(self):
                self.current_batch = 0
                return self
            
            def __next__(self):
                if self.current_batch >= self.num_batches:
                    raise StopIteration
                self.current_batch += 1
                return torch.randn(self.batch_size, self.seq_len, self.enc_in, device=device)
        
        dummy_loader = DummyDataLoader(cfg.batch_size, cfg.seq_len, cfg.enc_in)
        
        print("测试训练函数...")
        
        # 测试阶段1训练
        try:
            loss_g = stage1_train_generators(cfg, dummy_loader, models, optimizers, device)
            print(f"✅ 阶段1训练成功, 生成器损失: {loss_g:.4f}")
        except Exception as e:
            print(f"❌ 阶段1训练失败: {e}")
            return False
        
        # 测试阶段2训练
        try:
            loss_d = stage2_train_discriminators(cfg, dummy_loader, models, optimizers, device)
            print(f"✅ 阶段2训练成功, 判别器损失: {loss_d:.4f}")
        except Exception as e:
            print(f"❌ 阶段2训练失败: {e}")
            return False
        
        # 测试阶段3训练
        try:
            loss_g_joint, loss_d_joint = stage3_joint_train(cfg, dummy_loader, models, optimizers, device)
            print(f"✅ 阶段3训练成功, 生成器损失: {loss_g_joint:.4f}, 判别器损失: {loss_d_joint:.4f}")
        except Exception as e:
            print(f"❌ 阶段3训练失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 训练函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 train2.py 的 CLPF 模型移植")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    tests = [
        ("导入测试", test_imports),
        ("CLPF生成器测试", test_clpf_generator),
        ("模型组件测试", test_model_components),
        ("训练函数测试", test_training_functions),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！CLPF模型移植成功！")
        print("你现在可以运行 train2.py 进行完整训练了。")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
