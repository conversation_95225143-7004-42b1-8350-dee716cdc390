# Stage1 生成器测试功能说明

## 概述

在 `train2.py` 的 `stage1_train_generators` 函数中添加了生成器测试功能，用于在第一阶段训练完成后验证生成器的性能。

## 功能特点

### 1. 自动测试触发
- 在 `stage1_train_generators` 函数完成训练后自动调用
- 无需手动干预，集成在训练流程中

### 2. 对比分析
测试会对比以下两种序列：
- **直接生成序列**: CLPF生成器直接输出的趋势和季节序列
- **重构分解序列**: 生成序列 → 自编码器重构 → 处理器分解得到的序列

### 3. 多尺度可视化
- 为每个时间尺度创建独立的对比图
- 包含季节序列、趋势序列和组合序列的对比
- 显示完整的重构序列

### 4. 误差统计分析
- 计算MSE (均方误差) 和 MAE (平均绝对误差)
- 提供每个尺度的详细误差统计
- 计算总体平均误差

## 新增函数

### `test_generator_stage1(config, models, device)`
主测试函数，协调整个测试流程。

**参数:**
- `config`: 配置对象
- `models`: 模型元组 (processor, autoencoder, clpf_generator, d_season, d_trend, denormalizer)
- `device`: 计算设备

### `create_generator_comparison_plot(...)`
创建详细的对比可视化图。

**功能:**
- 多尺度序列对比
- 季节/趋势/组合序列分别显示
- 保存高质量PNG和PDF格式

### `calculate_reconstruction_errors(...)`
计算并显示重构误差统计。

**输出:**
- 每个尺度的MSE和MAE
- 平均误差统计
- 格式化的误差报告

## 输出文件

### 图片文件
```
stage1_test_results/
├── generator_comparison.png    # 高分辨率PNG格式
└── generator_comparison.pdf    # 矢量PDF格式
```

### 控制台输出
```
--- 开始生成器测试 ---

📈 重构误差分析:
==================================================
尺度 1:
  季节序列 - MSE: 0.123456, MAE: 0.234567
  趋势序列 - MSE: 0.345678, MAE: 0.456789
尺度 2:
  季节序列 - MSE: 0.567890, MAE: 0.678901
  趋势序列 - MSE: 0.789012, MAE: 0.890123
...

📊 平均误差:
  季节序列 - 平均MSE: 0.345678, 平均MAE: 0.456789
  趋势序列 - 平均MSE: 0.567890, 平均MAE: 0.678901
  总体平均MSE: 0.456784
  总体平均MAE: 0.567845
==================================================

📊 对比图已保存到: stage1_test_results/generator_comparison.png
📊 PDF版本已保存到: stage1_test_results/generator_comparison.pdf
✅ 生成器测试完成，对比图已保存
```

## 可视化图表说明

### 图表布局
- **行数**: 时间尺度数量 + 1
- **列数**: 3 (季节序列、趋势序列、组合序列)
- **最后一行**: 完整重构序列

### 每个子图包含
- **蓝色实线**: 生成器直接生成的序列
- **红色虚线**: 重构后分解的序列
- **网格线**: 便于读数
- **图例**: 清晰标识不同序列

### 颜色编码
- 🔵 **蓝色**: 生成器直接生成 (季节)
- 🔴 **红色**: 重构后分解 (季节)
- 🟢 **绿色**: 生成器直接生成 (趋势)
- 🟣 **紫色**: 重构后分解 (趋势)
- 🔵 **青色**: 生成器组合序列
- 🟠 **橙色**: 重构分解组合序列
- ⚫ **黑色**: 完整重构序列

## 使用方法

### 1. 自动使用
正常运行 `train2.py`，测试会在第一阶段训练完成后自动执行：

```python
python train2.py
```

### 2. 独立测试
可以使用提供的测试脚本验证功能：

```python
python test_stage1_generator.py
```

## 技术细节

### 数据流程
```
随机噪声 z → CLPF生成器 → (季节序列, 趋势序列)
                              ↓
                         自编码器重构
                              ↓
                         处理器分解
                              ↓
                    (重构季节序列, 重构趋势序列)
```

### 误差计算
- **MSE**: `torch.nn.functional.mse_loss()`
- **MAE**: `torch.nn.functional.l1_loss()`
- 对每个尺度分别计算，然后求平均

### 可视化特点
- 高分辨率输出 (300 DPI)
- 矢量PDF格式便于论文使用
- 中文标题和标签
- 专业的科学图表样式

## 预期结果

### 理想情况
- 直接生成序列与重构分解序列高度一致
- MSE和MAE误差较小 (< 0.1)
- 可视化图显示两条线基本重合

### 问题诊断
- **误差过大**: 可能需要调整训练参数或增加训练轮数
- **某个尺度误差特别大**: 可能该尺度的模型容量不足
- **趋势误差 > 季节误差**: 可能趋势建模更困难，需要特别关注

## 扩展建议

1. **添加更多指标**: 如相关系数、频域分析等
2. **批量测试**: 测试多个样本的平均性能
3. **时间序列特定指标**: 如DTW距离、频谱相似度等
4. **交互式可视化**: 使用plotly创建可交互的图表

这个测试功能为评估CLPF生成器的重构一致性提供了全面的工具，有助于调试和优化模型性能。
