import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, Dataset
from sklearn.preprocessing import MinMaxScaler

# --- Dataset Class Definition ---
# 将类定义移至模块顶层，这是更标准的写法
class SequenceDataset(Dataset):
    """Custom PyTorch Dataset for creating time series sequences."""
    def __init__(self, data, seq_len):
        self.data = data
        self.seq_len = seq_len

    def __getitem__(self, index):
        # Define the window for one sequence
        seq_x = self.data[index : index + self.seq_len]
        
        return torch.FloatTensor(seq_x)

    def __len__(self):
        # The total number of sequences we can create using a sliding window
        return len(self.data) - self.seq_len + 1

# --- Data Loading Function ---
def load_data(file_path, seq_len, batch_size):
    """
    Loads and preprocesses the entire dataset from a CSV file without splitting.
    The entire dataset is returned in a single DataLoader.

    Args:
        file_path (str): Path to the CSV data file.
        seq_len (int): Sequence length for the model input.
        batch_size (int): Batch size for the data loader.

    Returns:
        tuple: A tuple containing data_loader, None, None, and the number of features.
    """
    df_raw = pd.read_csv(file_path)
    
    # --- Feature Scaling ---
    # Use all columns except the first (assumed to be timestamp)
    df_features = df_raw.iloc[:, 0:]
    num_features = df_features.shape[1]
    
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(df_features.values)

    # --- DataLoader Creation ---
    # Create a dataset for the entire scaled data
    full_dataset = SequenceDataset(scaled_data, seq_len)
    
    # Create a single DataLoader for the entire dataset.
    full_loader = DataLoader(
        full_dataset, 
        batch_size=batch_size, 
        shuffle=False, 
        drop_last=False 
    )

    # Return the loader and num_features, with None for vali/test loaders
    # to maintain compatibility with the calling script.
    return full_loader, num_features
