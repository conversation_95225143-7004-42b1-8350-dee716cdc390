import numpy as np

def train_test_divide(data_x, data_x_hat, data_t, data_t_hat, train_rate=0.8):
    """
    Divide train and test data for both original and synthetic data.
    Refactored for clarity and efficiency with NumPy.

    Args:
      - data_x: original data (list of numpy arrays)
      - data_x_hat: generated data (list of numpy arrays)
      - data_t: original time (list of ints)
      - data_t_hat: generated time (list of ints)
      - train_rate: ratio of training data

    Returns:
      - train_x, train_x_hat, test_x, test_x_hat: Divided data for training and testing.
      - train_t, train_t_hat, test_t, test_t_hat: Divided time information.
    """
    # Convert to numpy arrays for efficient indexing
    data_x = np.asarray(data_x)
    data_x_hat = np.asarray(data_x_hat)
    data_t = np.asarray(data_t)
    data_t_hat = np.asarray(data_t_hat)

    # Divide original data
    no = len(data_x)
    idx = np.random.permutation(no)
    train_idx = idx[:int(no * train_rate)]
    test_idx = idx[int(no * train_rate):]

    train_x = data_x[train_idx]
    test_x = data_x[test_idx]
    train_t = data_t[train_idx]
    test_t = data_t[test_idx]

    # Divide synthetic data
    no_hat = len(data_x_hat)
    idx_hat = np.random.permutation(no_hat)
    train_idx_hat = idx_hat[:int(no_hat * train_rate)]
    test_idx_hat = idx_hat[int(no_hat * train_rate):]

    train_x_hat = data_x_hat[train_idx_hat]
    test_x_hat = data_x_hat[test_idx_hat]
    train_t_hat = data_t_hat[train_idx_hat]
    test_t_hat = data_t_hat[test_idx_hat]

    # Return lists, as expected by the original metric functions
    return list(train_x), list(train_x_hat), list(test_x), list(test_x_hat), \
           list(train_t), list(train_t_hat), list(test_t), list(test_t_hat)


def MinMaxScaler(data):
    """Min-Max Normalizer.

    Args:
      - data: raw data

    Returns:
      - norm_data: normalized data
      - min_val: minimum values (for renormalization)
      - max_val: maximum values (for renormalization)
    """
    min_val = np.min(np.min(data, axis=0), axis=0)
    data = data - min_val

    max_val = np.max(np.max(data, axis=0), axis=0)
    norm_data = data / (max_val + 1e-7)

    return norm_data, min_val, max_val


def extract_time(data):
    """
    Returns Maximum sequence length and each sequence length.
    This function is kept for compatibility with the original TimeGAN metric structure.
    For fixed-length data, it will return a list of identical sequence lengths.

    Args:
      - data: list of numpy arrays

    Returns:
      - time: a list containing the sequence length of each sample.
      - max_seq_len: the maximum sequence length in the dataset.
    """
    time = []
    max_seq_len = 0
    for i in range(len(data)):
        # Assumes data is a list of arrays, each with shape (seq_len, features)
        seq_len = len(data[i])
        time.append(seq_len)
        if seq_len > max_seq_len:
            max_seq_len = seq_len
            
    return time, max_seq_len


def random_generator(batch_size, z_dim, max_seq_len, T_mb=None):
    """Random vector generation.

    Args:
      - batch_size: size of the random vector
      - z_dim: dimension of random vector
      - max_seq_len: maximum sequence length
      - T_mb: (Optional) time information for the random vector

    Returns:
      - Z_mb: generated random vector
    """
    Z_mb = []
    for i in range(batch_size):
        # If time information is not provided, use max_seq_len
        seq_len = T_mb[i] if T_mb is not None else max_seq_len
        temp = np.random.uniform(0., 1, [seq_len, z_dim])
        Z_mb.append(temp)
    return Z_mb


def batch_generator(data, time, batch_size):
    """
    Mini-batch generator.
    Refactored to handle numpy arrays efficiently.

    Args:
      - data: time-series data (list or numpy array)
      - time: time information (list or numpy array)
      - batch_size: the number of samples in each batch

    Returns:
      - X_mb: time-series data in each batch (list of numpy arrays)
      - T_mb: time information in each batch (list of ints)
    """
    # Ensure data is a numpy array for efficient indexing
    data = np.asarray(data)
    if time is not None:
        time = np.asarray(time)

    no = len(data)
    idx = np.random.permutation(no)
    
    # Use min to handle the case where the dataset size is smaller than the batch size
    eff_batch_size = min(batch_size, no)
    batch_idx = idx[:eff_batch_size]

    X_mb = [data[i] for i in batch_idx]
    T_mb = [time[i] for i in batch_idx] if time is not None else None

    return X_mb, T_mb
