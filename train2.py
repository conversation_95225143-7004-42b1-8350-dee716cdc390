import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ['TF_XLA_FLAGS'] = '--tf_xla_enable_xla_devices'
os.environ['TF_CPP_MIN_LOG_LEVEL']='2'

import random
import warnings
import numpy as np
import torch
import torch.nn as nn
from torch import optim
from typing import List, Tuple
import matplotlib.pyplot as plt
from torch.nn.utils import spectral_norm
import shutil

try:
    from data_loader import load_data
    from torchdiffeq import odeint
    from metrics.visualization_metrics import visualization
    from metrics.quantitative_metrics import discriminative_score_metrics, predictive_score_metrics
except ImportError as e:
    print(f"Import Error: {e}")
    exit()

class Config:
    file_path = 'data/stock_data.csv'
    seq_len = 24
    batch_size = 32
    stage1_epochs = 50
    stage2_epochs = 50
    stage3_epochs = 200
    lr_g = 0.0001; lr_d = 0.0001; betas = (0.5, 0.9)
    latent_dim = 128
    lambda_gp = 15.0
    lambda_cycle = 25.0
    lambda_recon = 50.0 
    critic_iterations = 5
    metric_iteration = 10
    processor_path = 'pretrained_models/processor_focal.pth'
    autoencoder_path = 'pretrained_models/reconstruction_decoder_focal.pth'
    output_dir = 'gan_outputs_staged'

    timemixer_params = {
        'd_model': 768, 
        'd_ff': 3072, 
        'dropout': 0.1,
        'down_sampling_window': 2, 
        'down_sampling_layers': 3, 
        'moving_avg': 9
    }
    pcf_disc_hidden_dim = 256
    pcf_disc_lstm_layers = 2

    def update_dims(self, num_features):
        self.enc_in = num_features
        self.c_out = num_features
        self.timemixer_params.update({
            'enc_in': num_features, 'c_out': num_features, 'seq_len': self.seq_len
        })
cfg = Config()


class series_decomp(nn.Module):
    def __init__(self, kernel_size):
        super(series_decomp, self).__init__()
        self.moving_avg = nn.AvgPool1d(kernel_size=kernel_size, stride=1, padding=(kernel_size - 1) // 2)
    def forward(self, x):
        moving_mean = self.moving_avg(x.permute(0, 2, 1)).permute(0, 2, 1)
        res = x - moving_mean
        return res, moving_mean

class DataEmbedding_wo_pos(nn.Module):
    def __init__(self, c_in, d_model, dropout=0.1):
        super(DataEmbedding_wo_pos, self).__init__()
        self.value_embedding = nn.Linear(c_in, d_model)
        self.dropout = nn.Dropout(p=dropout)
    def forward(self, x, x_mark):
        x = self.value_embedding(x)
        return self.dropout(x)

class Normalize(nn.Module):
    def __init__(self, num_features: int, eps=1e-5, affine=True):
        super(Normalize, self).__init__()
        self.eps = eps
        self.affine = affine
        if self.affine:
            self.gamma = nn.Parameter(torch.ones(1, 1, num_features))
            self.beta = nn.Parameter(torch.zeros(1, 1, num_features))
    def forward(self, x, mode: str):
        if mode == 'norm':
            self._get_statistics(x)
            x = self._normalize(x)
            return x, self.mean, self.stdev
        elif mode == 'denorm':
            x = self._denormalize(x)
            return x
        else: raise NotImplementedError
    def _get_statistics(self, x):
        self.mean = torch.mean(x, dim=1, keepdim=True).detach()
        self.stdev = torch.sqrt(torch.var(x, dim=1, keepdim=True, unbiased=False) + self.eps).detach()
    def _normalize(self, x):
        x = (x - self.mean) / self.stdev
        if self.affine:
            x = x * self.gamma + self.beta
        return x
    def _denormalize(self, x):
        if not hasattr(self, 'mean') or not hasattr(self, 'stdev'):
             raise RuntimeError("Normalization stats not found. Call in 'norm' mode first or provide them.")
        if self.affine:
            x = (x - self.beta) / (self.gamma + self.eps * self.eps)
        x = x * self.stdev + self.mean
        return x

class DecompositionalEncoder(nn.Module):
    def __init__(self, config: Config):
        super(DecompositionalEncoder, self).__init__()
        tm_params = config.timemixer_params
        self.down_sampling_window = tm_params['down_sampling_window']
        self.down_sampling_layers = tm_params['down_sampling_layers']
        self.enc_in = tm_params['enc_in']
        self.down_pool = nn.AvgPool1d(kernel_size=self.down_sampling_window, stride=self.down_sampling_window)
        self.normalize_layers = nn.ModuleList([Normalize(self.enc_in, affine=True) for _ in range(self.down_sampling_layers + 1)])
        self.enc_embedding = DataEmbedding_wo_pos(
            tm_params['enc_in'], tm_params['d_model'], tm_params['dropout']
        )
        self.decomposition = series_decomp(tm_params['moving_avg'])
    def forward(self, x_enc: torch.Tensor) -> Tuple[List[torch.Tensor], List[torch.Tensor], torch.Tensor, torch.Tensor]:
        x_enc_transposed = x_enc.permute(0, 2, 1)
        multi_scale_inputs = [x_enc]
        current_x = x_enc_transposed
        for _ in range(self.down_sampling_layers):
            current_x = self.down_pool(current_x)
            multi_scale_inputs.append(current_x.permute(0, 2, 1))
        season_list, trend_list = [], []
        main_mean, main_stdev = None, None
        for i, x in enumerate(multi_scale_inputs):
            x_norm, mean, stdev = self.normalize_layers[i](x, 'norm')
            if i == 0: main_mean, main_stdev = mean, stdev
            embedded_x = self.enc_embedding(x_norm, None)
            season, trend = self.decomposition(embedded_x)
            season_list.append(season)
            trend_list.append(trend)
        return season_list, trend_list, main_mean, main_stdev

class ReconstructionDecoder(nn.Module):
    def __init__(self, config: Config):
        super(ReconstructionDecoder, self).__init__()
        tm_params = config.timemixer_params
        self.season_mixer = MultiScaleSeasonMixing(config)
        self.trend_mixer = MultiScaleTrendMixing(config)
        self.projection_layer = nn.Linear(tm_params['d_model'], tm_params['c_out'])
    def forward(self, season_list: List[torch.Tensor], trend_list: List[torch.Tensor]) -> torch.Tensor:
        season_list_permuted = [s.permute(0, 2, 1) for s in season_list]
        trend_list_permuted = [t.permute(0, 2, 1) for t in trend_list]
        mixed_seasons_permuted = self.season_mixer(season_list_permuted)
        mixed_trends_permuted = self.trend_mixer(trend_list_permuted)
        combined_permuted = mixed_seasons_permuted[0] + mixed_trends_permuted[0]
        combined = combined_permuted.permute(0, 2, 1)
        reconstructed_norm = self.projection_layer(combined)
        return reconstructed_norm

class MultiScaleSeasonMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleSeasonMixing, self).__init__()
        seq_len = config.seq_len
        dsw = config.timemixer_params['down_sampling_window']
        dsl = config.timemixer_params['down_sampling_layers']
        self.down_sampling_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** (i + 1))), 
                nn.GELU(), 
                nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** (i + 1))),
            ) for i in range(dsl)
        ])
    def forward(self, season_list_permuted):
        out_high = season_list_permuted[0]; out_low = season_list_permuted[1]; out_season_list = [out_high]
        for i in range(len(season_list_permuted) - 1):
            out_low_res = self.down_sampling_layers[i](out_high); out_low = out_low + out_low_res; out_high = out_low
            if i + 2 <= len(season_list_permuted) - 1: out_low = season_list_permuted[i + 2]
            out_season_list.append(out_high)
        return out_season_list

class MultiScaleTrendMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleTrendMixing, self).__init__()
        seq_len = config.seq_len
        dsw = config.timemixer_params['down_sampling_window']
        dsl = config.timemixer_params['down_sampling_layers']
        self.up_sampling_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** i)), 
                nn.GELU(), 
                nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** i)),
            ) for i in reversed(range(dsl))
        ])
    def forward(self, trend_list_permuted):
        trend_list_reverse = trend_list_permuted.copy(); trend_list_reverse.reverse(); out_low = trend_list_reverse[0]; out_high = trend_list_reverse[1]; out_trend_list = [out_low]
        for i in range(len(trend_list_reverse) - 1):
            out_high_res = self.up_sampling_layers[i](out_low); out_high = out_high + out_high_res; out_low = out_high
            if i + 2 <= len(trend_list_reverse) - 1: out_high = trend_list_reverse[i + 2]
            out_trend_list.append(out_low)
        out_trend_list.reverse(); return out_trend_list

class ODEFunc(nn.Module):
    def __init__(self, data_dim: int):
        super(ODEFunc, self).__init__(); self.net = nn.Sequential(nn.Linear(data_dim, 512), nn.Tanh(), nn.Linear(512, 1024), nn.Tanh(), nn.Linear(1024, 512), nn.Tanh(), nn.Linear(512, data_dim))
    def forward(self, t, x): return self.net(x)

class CLPFGenerator(nn.Module):
    def __init__(self, latent_dim: int, config: Config):
        super(CLPFGenerator, self).__init__(); self.config = config; self.output_dim = 0
        tm_params = config.timemixer_params
        for i in range(tm_params['down_sampling_layers'] + 1):
            seq_len_i = config.seq_len // (tm_params['down_sampling_window'] ** i)
            self.output_dim += seq_len_i * tm_params['d_model']
        self.ode_func = ODEFunc(self.output_dim); self.z_mapper = nn.Linear(latent_dim, self.output_dim); self.integration_time = torch.tensor([0, 1]).float()
    def forward(self, z: torch.Tensor) -> List[torch.Tensor]:
        batch_size = z.size(0); x0 = self.z_mapper(z); t = self.integration_time.to(z.device)
        states = odeint(self.ode_func, x0, t, method='dopri5', rtol=1e-4, atol=1e-5)
        final_state = states[1]; component_list = []; current_pos = 0
        tm_params = self.config.timemixer_params
        for i in range(tm_params['down_sampling_layers'] + 1):
            seq_len_i = self.config.seq_len // (tm_params['down_sampling_window'] ** i)
            component_size = seq_len_i * tm_params['d_model']
            component = final_state[:, current_pos : current_pos + component_size]
            component_list.append(component.view(batch_size, seq_len_i, tm_params['d_model']))
            current_pos += component_size
        return component_list

class LSTMBasedDiscriminator(nn.Module):
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int):
        super().__init__(); self.rnn = nn.LSTM(input_size=input_dim, hidden_size=hidden_dim, num_layers=num_layers, batch_first=True); self.output_dim = hidden_dim
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        _, (h_n, _) = self.rnn(x); return h_n[-1, :, :]

class PCFDiscriminator(nn.Module):
    def __init__(self, config: Config):
        super(PCFDiscriminator, self).__init__()
        self.scale_discriminators = nn.ModuleList()
        final_mlp_input_dim = 0
        tm_params = config.timemixer_params
        num_scales = tm_params['down_sampling_layers'] + 1
        for _ in range(num_scales):
            scale_d = LSTMBasedDiscriminator(
                input_dim=tm_params['d_model'],
                hidden_dim=config.pcf_disc_hidden_dim,
                num_layers=config.pcf_disc_lstm_layers
            )
            self.scale_discriminators.append(scale_d)
            final_mlp_input_dim += scale_d.output_dim
        self.final_mlp = nn.Sequential(spectral_norm(nn.Linear(final_mlp_input_dim, 256)), nn.LeakyReLU(0.2), spectral_norm(nn.Linear(256, 1)))
    def forward(self, series_list: List[torch.Tensor]) -> torch.Tensor:
        scale_features = [self.scale_discriminators[i](series) for i, series in enumerate(series_list)]
        combined_features = torch.cat(scale_features, dim=1)
        return self.final_mlp(combined_features)

def compute_gradient_penalty(discriminator, real_samples, fake_samples, device):
    real_flat = torch.cat([s.reshape(s.size(0), -1) for s in real_samples], dim=1); fake_flat = torch.cat([s.reshape(s.size(0), -1) for s in fake_samples], dim=1)
    alpha = torch.randn(real_flat.size(0), 1, device=device); interpolates_flat = (alpha * real_flat + ((1 - alpha) * fake_flat)).requires_grad_(True)
    interp_list = []; current_pos = 0
    for real_s in real_samples:
        size = real_s.shape[1] * real_s.shape[2]; interp_list.append(interpolates_flat[:, current_pos:current_pos+size].view_as(real_s)); current_pos += size
    with torch.backends.cudnn.flags(enabled=False): d_interpolates = discriminator(interp_list)
    grad_outputs = torch.ones(d_interpolates.size(), device=device, requires_grad=False)
    gradients = torch.autograd.grad(outputs=d_interpolates, inputs=interpolates_flat, grad_outputs=grad_outputs, create_graph=True, retain_graph=True, only_inputs=True)[0]
    gradients = gradients.view(gradients.size(0), -1)
    return ((gradients.norm(2, dim=1) - 1) ** 2).mean()


def stage1_train_generators(config, data_loader, models, optimizers, device):
    processor, autoencoder, g_season, g_trend, _, _, _ = models
    optimizer_G = optimizers['G']
    criterion_l1 = nn.L1Loss()
    processor.eval(); autoencoder.eval(); g_season.train(); g_trend.train()

    print("\n--- [开始第一阶段]: 单独训练生成器 (循环一致性损失) ---")
    for epoch in range(config.stage1_epochs):
        total_g_loss = 0
        # This part assumes a data loader that doesn't necessarily need labels
        for batch_data in data_loader:
            batch_x = batch_data[0] if isinstance(batch_data, (list, tuple)) else batch_data
            b_size = batch_x.size(0)
            
            optimizer_G.zero_grad()
            z_season = torch.randn(b_size, config.latent_dim, device=device)
            z_trend = torch.randn(b_size, config.latent_dim, device=device)
            fake_season_list = g_season(z_season)
            fake_trend_list = g_trend(z_trend)

            # This part of the logic relies on the fake data and doesn't use the input batch
            # so the training loop structure is slightly different from discriminator training
            reconstructed_norm_from_fake = autoencoder(fake_season_list, fake_trend_list)
            # We need a proxy for denormalization, but since it's about cycle consistency,
            # we can work in the normalized space. The goal is G(Z) -> AE -> P -> G(Z).
            # The issue is P requires non-normalized input. 
            # Let's assume we can re-decompose the normalized output for a cycle loss.
            redecomposed_season, redecomposed_trend, _, _ = processor(reconstructed_norm_from_fake)

            loss_cycle_s = sum(criterion_l1(fake_season_list[j], redecomposed_season[j]) for j in range(len(fake_season_list)))
            loss_cycle_t = sum(criterion_l1(fake_trend_list[j], redecomposed_trend[j]) for j in range(len(fake_trend_list)))
            loss_g_cycle = loss_cycle_s + loss_cycle_t
            loss_g_cycle.backward()
            optimizer_G.step()
            total_g_loss += loss_g_cycle.item()

        avg_g_loss = total_g_loss / len(data_loader) if len(data_loader) > 0 else 0
        print(f"[阶段 1, Epoch {epoch+1}/{config.stage1_epochs}] 生成器循环损失: {avg_g_loss:.4f}")
    print("--- 第一阶段训练完成 ---")


def stage2_train_discriminators(config, data_loader, models, optimizers, device):
    processor, _, g_season, g_trend, d_season, d_trend, _ = models
    optimizer_D = optimizers['D']
    processor.eval(); g_season.eval(); g_trend.eval(); d_season.train(); d_trend.train()
    
    print("\n--- [开始第二阶段]: 单独训练判别器 ---")
    for epoch in range(config.stage2_epochs):
        total_d_loss = 0
        for batch_data in data_loader:
            batch_x = batch_data[0] if isinstance(batch_data, (list, tuple)) else batch_data
            batch_x = batch_x.float().to(device)
            b_size = batch_x.size(0)
            optimizer_D.zero_grad()

            with torch.no_grad():
                real_season, real_trend, _, _ = processor(batch_x)
                z = torch.randn(b_size, config.latent_dim, device=device)
                fake_season = [s.detach() for s in g_season(z)]
                fake_trend = [t.detach() for t in g_trend(z)]

            loss_d_season = d_season(fake_season).mean() - d_season(real_season).mean()
            loss_d_trend = d_trend(fake_trend).mean() - d_trend(real_trend).mean()
            gp_season = compute_gradient_penalty(d_season, real_season, fake_season, device)
            gp_trend = compute_gradient_penalty(d_trend, real_trend, fake_trend, device)
            loss_d = loss_d_season + loss_d_trend + config.lambda_gp * (gp_season + gp_trend)
            loss_d.backward()
            optimizer_D.step()
            total_d_loss += loss_d.item()
        
        avg_d_loss = total_d_loss / len(data_loader) if len(data_loader) > 0 else 0
        print(f"[阶段 2, Epoch {epoch+1}/{config.stage2_epochs}] 判别器损失: {avg_d_loss:.4f}")
    print("--- 第二阶段训练完成 ---")


def stage3_joint_train(config, data_loader, models, optimizers, device):
    processor, autoencoder, g_season, g_trend, d_season, d_trend, denormalizer = models
    optimizer_G_PA = optimizers['G_PA']; optimizer_D = optimizers['D']
    criterion_l1 = nn.L1Loss()
    for model in models: 
        if model is not None: model.train()
    
    print("\n--- [开始第三阶段]: 联合训练所有网络 ---")
    for epoch in range(config.stage3_epochs):
        for i, batch_data in enumerate(data_loader):
            batch_x = batch_data[0] if isinstance(batch_data, (list, tuple)) else batch_data
            batch_x = batch_x.float().to(device)
            b_size = batch_x.size(0)

            # --- 1. 训练判别器 ---
            optimizer_D.zero_grad()
            with torch.no_grad():
                real_season_d, real_trend_d, _, _ = processor(batch_x)
                z_d = torch.randn(b_size, config.latent_dim, device=device)
                fake_season_d = [s.detach() for s in g_season(z_d)]
                fake_trend_d = [t.detach() for t in g_trend(z_d)]
            loss_d_s = d_season(fake_season_d).mean() - d_season(real_season_d).mean()
            loss_d_t = d_trend(fake_trend_d).mean() - d_trend(real_trend_d).mean()
            gp_s = compute_gradient_penalty(d_season, real_season_d, fake_season_d, device)
            gp_t = compute_gradient_penalty(d_trend, real_trend_d, fake_trend_d, device)
            loss_d = loss_d_s + loss_d_t + config.lambda_gp * (gp_s + gp_t)
            loss_d.backward()
            optimizer_D.step()

            # --- 2. 训练生成器、编码器和解码器 ---
            if (i + 1) % config.critic_iterations == 0:
                optimizer_G_PA.zero_grad()
                
                real_season_g, real_trend_g, mean_g, stdev_g = processor(batch_x)
                z_g = torch.randn(b_size, config.latent_dim, device=device)
                fake_season_g = g_season(z_g)
                fake_trend_g = g_trend(z_g)
                
                loss_g_adv = -d_season(fake_season_g).mean() - d_trend(fake_trend_g).mean()
                
                reconstructed_norm = autoencoder(fake_season_g, fake_trend_g)
                denormalizer.mean = mean_g; denormalizer.stdev = stdev_g
                reconstructed_output = denormalizer(reconstructed_norm, 'denorm')
                # This reconstruction loss is a bit unusual; it compares G(Z)->AE->X' with the real X
                # A more common approach is identity loss: G(P(X))->AE->X'' vs X
                loss_g_recon = criterion_l1(reconstructed_output, batch_x) 
                
                # Cycle consistency loss from fake to re-decomposed
                redec_s, redec_t, _, _ = processor(reconstructed_norm.detach())
                loss_g_cycle = sum(criterion_l1(fake_season_g[j], redec_s[j]) for j in range(len(fake_season_g))) + \
                               sum(criterion_l1(fake_trend_g[j], redec_t[j]) for j in range(len(fake_trend_g)))
                
                loss_g_total = loss_g_adv + config.lambda_recon * loss_g_recon + config.lambda_cycle * loss_g_cycle
                loss_g_total.backward()
                optimizer_G_PA.step()

        if 'loss_g_total' in locals() and 'loss_d' in locals():
            print(f"[阶段 3, Epoch {epoch+1}/{config.stage3_epochs}] D Loss: {loss_d.item():.4f}, G Loss: {loss_g_total.item():.4f}")
    print("--- 第三阶段训练完成 ---")


# --- 5. Testing Function (Unchanged) ---
def test_model(config, data_loader, models):
    processor, autoencoder, g_season, g_trend, _, _, denormalizer = models
    device = next(processor.parameters()).device
    print("\n--- 开始最终评估和可视化 ---")
    for m in models: 
        if m is not None: m.eval()
    
    # Adapt to data_loader potentially returning tuples
    ori_data_list = []
    for batch_data in data_loader:
        batch_x = batch_data[0] if isinstance(batch_data, (list, tuple)) else batch_data
        ori_data_list.append(batch_x.numpy())
    ori_data_full = np.concatenate(ori_data_list, axis=0)
    synth_size = len(ori_data_full)
    print(f"已收集原始数据。总样本数: {synth_size}")

    generated_data_list = []
    real_data_iter = iter(data_loader)
    with torch.no_grad():
        for i in range(0, synth_size, config.batch_size):
            b_size = min(config.batch_size, synth_size - i)
            if b_size == 0: continue
            
            try:
                real_batch_data = next(real_data_iter)
                real_batch_x = real_batch_data[0] if isinstance(real_batch_data, (list, tuple)) else real_batch_data
                _, _, mean, stdev = processor(real_batch_x.to(device))
            except StopIteration: 
                # This logic is risky if the test set size is not a multiple of batch size
                # but we'll follow the original structure.
                real_data_iter = iter(data_loader)
                real_batch_data = next(real_data_iter)
                real_batch_x = real_batch_data[0] if isinstance(real_batch_data, (list, tuple)) else real_batch_data
                _, _, mean, stdev = processor(real_batch_x.to(device))

            z = torch.randn(b_size, config.latent_dim, device=device)
            fake_s = g_season(z); fake_t = g_trend(z)
            fake_recon_norm = autoencoder(fake_s, fake_t)
            
            denormalizer.mean = mean; denormalizer.stdev = stdev
            fake_recon = denormalizer(fake_recon_norm, 'denorm')
            generated_data_list.append(fake_recon.cpu().numpy())

    generated_data_full = np.concatenate(generated_data_list, axis=0)
    print(f"已生成合成数据。总样本数: {len(generated_data_full)}")
    
    visualization(ori_data_full, generated_data_full, 'pca', config.output_dir, "final_gan_output_pca")
    visualization(ori_data_full, generated_data_full, 'tsne', config.output_dir, "final_gan_output_tsne")
    print(f"图表已保存至 '{config.output_dir}' 目录。")


if __name__ == '__main__':
    random.seed(8760); np.random.seed(8760); torch.manual_seed(8760); torch.cuda.manual_seed_all(8760)
    warnings.filterwarnings('ignore'); device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    if os.path.exists(cfg.output_dir): shutil.rmtree(cfg.output_dir)
    os.makedirs(cfg.output_dir, exist_ok=True); os.makedirs('pretrained_models', exist_ok=True)
    
    print(f"--- 运行设备: {device} ---")
    # ✨ MODIFICATION: Synced data loading call with the first script
    data_loader, num_features = load_data(
        file_path=cfg.file_path, seq_len=cfg.seq_len, batch_size=cfg.batch_size
    )
    cfg.update_dims(num_features)
    print(f"数据已加载. 特征数: {num_features}. 模型维度 d_model: {cfg.timemixer_params['d_model']}")

    processor = DecompositionalEncoder(cfg).to(device)
    autoencoder = ReconstructionDecoder(cfg).to(device)
    g_season = CLPFGenerator(cfg.latent_dim, cfg).to(device)
    g_trend = CLPFGenerator(cfg.latent_dim, cfg).to(device)
    d_season = PCFDiscriminator(cfg).to(device)
    d_trend = PCFDiscriminator(cfg).to(device)
    denormalizer = Normalize(num_features, affine=True).to(device)
    
    models = (processor, autoencoder, g_season, g_trend, d_season, d_trend, denormalizer)
    
    try:
        processor.load_state_dict(torch.load(cfg.processor_path, map_location=device))
        autoencoder.load_state_dict(torch.load(cfg.autoencoder_path, map_location=device))
        print("✅ 已成功加载预训练的 Processor 和 Decoder。")
    except FileNotFoundError as e:
        print(f"⚠️ 警告: 未找到预训练模型 '{e.filename}'。将从头开始训练所有模块。")
        # In a real scenario, you might exit or pre-train here.
        # For now, we proceed with randomly initialized P and AE.

    optimizer_G = optim.Adam(list(g_season.parameters()) + list(g_trend.parameters()), lr=cfg.lr_g, betas=cfg.betas)
    optimizer_D = optim.Adam(list(d_season.parameters()) + list(d_trend.parameters()), lr=cfg.lr_d, betas=cfg.betas)
    # The joint optimizer should probably not train the pre-trained models, but we follow the original script's logic.
    optimizer_G_PA = optim.Adam(list(g_season.parameters()) + list(g_trend.parameters()) + list(processor.parameters()) + list(autoencoder.parameters()), lr=cfg.lr_g, betas=cfg.betas)
    optimizers = {'G': optimizer_G, 'D': optimizer_D, 'G_PA': optimizer_G_PA}
    
    # Staged training calls now use a consistent data loader format
    # I have adjusted the training loops slightly to handle the data loader output correctly
    # Note: stage1_train_generators has a logical flaw in the original code, as it doesn't use the input data
    # to guide the cycle consistency. I've kept the logic but it might not perform as expected.
    stage1_train_generators(cfg, data_loader, models, optimizers, device)
    stage2_train_discriminators(cfg, data_loader, models, optimizers, device)
    stage3_joint_train(cfg, data_loader, models, optimizers, device)
    
    torch.save(g_season.state_dict(), os.path.join('pretrained_models', 'final_generator_season.pth'))
    torch.save(g_trend.state_dict(), os.path.join('pretrained_models', 'final_generator_trend.pth'))
    print("--- 🏁 所有训练阶段完成. 最终生成器模型已保存。 ---")
    
    test_model(cfg, data_loader, models)