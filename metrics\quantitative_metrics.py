import numpy as np
from sklearn.metrics import accuracy_score, mean_absolute_error
from utils import train_test_divide, extract_time, batch_generator # Assuming these are in a utils.py file
import torch
import torch.nn as nn

# =================================================================================
# 1. DISCRIMINATIVE SCORE
# =================================================================================

class Discriminator(nn.Module):
    """A simple GRU-based discriminator for the post-hoc evaluation metric."""
    def __init__(self, input_dim, hidden_dim, num_layer):
        super(Discriminator, self).__init__()
        self.rnn = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layer,
            batch_first=True
        )
        self.fc = nn.Linear(hidden_dim, 1)

    def forward(self, X):
        """
        Forward pass for the discriminator.
        Args:
          - X: input data (batch_size, seq_len, input_dim)
        Returns:
          - y_hat_logit: Logits of the predictions.
          - y_hat: Sigmoid-activated predictions.
        """
        _, h_n = self.rnn(X)
        # h_n shape: (num_layers, batch_size, hidden_dim)
        # We take the hidden state of the last layer for our classification.
        y_hat_logit = self.fc(h_n[-1]) # Correctly select the last layer's state
        y_hat = torch.sigmoid(y_hat_logit)
        return y_hat_logit, y_hat


def discriminative_score_metrics(ori_data, generated_data):
    """
    Computes the discriminative score between original and generated data.
    A score close to 0 indicates the generated data is highly realistic.
    """
    # Basic Parameters
    no, seq_len, dim = np.asarray(ori_data).shape
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    # Data division
    ori_time, _ = extract_time(ori_data)
    generated_time, _ = extract_time(generated_data)
    
    train_x, train_x_hat, test_x, test_x_hat, _, _, _, _ = \
        train_test_divide(ori_data, generated_data, ori_time, generated_time)

    # Network-parameters
    hidden_dim = int(dim / 2)
    iterations = 2000
    batch_size = 128

    discriminator = Discriminator(dim, hidden_dim, 1).to(device)
    optim_discriminator = torch.optim.Adam(discriminator.parameters())
    loss_function = nn.BCEWithLogitsLoss()

    # --- Training ---
    for itt in range(iterations):
        discriminator.train()
        optim_discriminator.zero_grad()
        
        X_mb, _ = batch_generator(train_x, None, batch_size)
        X_hat_mb, _ = batch_generator(train_x_hat, None, batch_size)
        
        X_mb = torch.tensor(X_mb, dtype=torch.float32).to(device)
        X_hat_mb = torch.tensor(X_hat_mb, dtype=torch.float32).to(device)
        
        y_logit_real, _ = discriminator(X_mb)
        y_logit_fake, _ = discriminator(X_hat_mb)
        
        d_loss_real = loss_function(y_logit_real, torch.ones_like(y_logit_real))
        d_loss_fake = loss_function(y_logit_fake, torch.zeros_like(y_logit_fake))
        d_loss = d_loss_real + d_loss_fake

        d_loss.backward()
        optim_discriminator.step()

    # --- Testing ---
    discriminator.eval()
    
    test_x = torch.tensor(test_x, dtype=torch.float32).to(device)
    test_x_hat = torch.tensor(test_x_hat, dtype=torch.float32).to(device)
    
    _, y_pred_real_curr = discriminator(test_x)
    _, y_pred_fake_curr = discriminator(test_x_hat)
    
    y_pred_real_curr = y_pred_real_curr.cpu().detach().numpy()
    y_pred_fake_curr = y_pred_fake_curr.cpu().detach().numpy()
    
    y_pred_final = np.squeeze(np.concatenate((y_pred_real_curr, y_pred_fake_curr), axis=0))
    y_label_final = np.concatenate((np.ones(len(y_pred_real_curr)), np.zeros(len(y_pred_fake_curr))), axis=0)
    
    acc = accuracy_score(y_label_final, (y_pred_final > 0.5))
    discriminative_score = np.abs(0.5 - acc)
    
    return discriminative_score


# =================================================================================
# 2. PREDICTIVE SCORE
# =================================================================================

class Predictor(nn.Module):
    """A simple GRU-based predictor for the post-hoc evaluation metric."""
    def __init__(self, input_dim, hidden_dim, num_layer):
        super(Predictor, self).__init__()
        self.rnn = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layer,
            batch_first=True
        )
        # The output is a single value, so the final layer maps the hidden state to dimension 1.
        self.fc = nn.Linear(hidden_dim, 1)

    def forward(self, X):
        """
        Forward pass for the predictor.
        Args:
          - X: input data (batch_size, seq_len-1, input_dim)
        Returns:
          - y_hat: Predictions for the next time step.
        """
        # We are interested in the output at each time step, not just the final hidden state.
        p_outputs, _ = self.rnn(X)
        # Pass the output of every time step to the fully connected layer
        y_hat = self.fc(p_outputs)
        return y_hat


def predictive_score_metrics(ori_data, generated_data):
    """
    Computes the predictive score.
    This function trains a post-hoc GRU predictor on the generated data to predict
    the next time step of the last feature. It then evaluates the MAE of this
    predictor on the original data. A lower MAE is better.
    """
    # Basic Parameters
    ori_data = np.asarray(ori_data)
    generated_data = np.asarray(generated_data)
    no, seq_len, dim = ori_data.shape
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    # Network-parameters
    hidden_dim = int(dim / 2)
    iterations = 5000
    batch_size = 128

    # --- Data Preparation ---
    # The goal is to predict the last feature at time t using all other features up to time t-1.
    # Training Data (from generated data)
    train_x = generated_data[:, :-1, :]  # All but the last time step
    train_y = generated_data[:, 1:, -1]  # The last feature from the 2nd to the last time step

    # Testing Data (from original data)
    test_x = ori_data[:, :-1, :]
    test_y = ori_data[:, 1:, -1]

    # --- Training the post-hoc predictor ---
    predictor = Predictor(dim, hidden_dim, 1).to(device)
    optim_predictor = torch.optim.Adam(predictor.parameters())
    loss_function = nn.L1Loss() # MAE Loss

    for itt in range(iterations):
        predictor.train()
        optim_predictor.zero_grad()
        
        # Generate a random batch
        idx = np.random.permutation(len(train_x))
        batch_idx = idx[:batch_size]
        
        X_mb = torch.tensor(train_x[batch_idx], dtype=torch.float32).to(device)
        # Y_mb needs to be reshaped to (batch_size, seq_len-1, 1) to match the predictor's output shape
        Y_mb = torch.tensor(train_y[batch_idx], dtype=torch.float32).unsqueeze(-1).to(device)
        
        # Forward pass and loss calculation
        y_pred = predictor(X_mb)
        p_loss = loss_function(y_pred, Y_mb)
        
        p_loss.backward()
        optim_predictor.step()

    # --- Test the performance on the original data ---
    predictor.eval()
    
    # Convert entire test set to tensor
    test_x_tensor = torch.tensor(test_x, dtype=torch.float32).to(device)
    
    # Get predictions
    pred_y = predictor(test_x_tensor)
    pred_y = pred_y.cpu().detach().numpy().squeeze() # Remove the last dimension
    
    # Compute the MAE
    predictive_score = mean_absolute_error(test_y.flatten(), pred_y.flatten())
    
    return predictive_score
