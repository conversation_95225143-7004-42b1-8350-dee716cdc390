# Simplified diffeq solver for CLPF
import torch
import torch.nn as nn
from torchdiffeq import odeint


class DiffeqSolver(nn.Module):
    def __init__(self, input_dim, ode_func, method, latent_dim, 
                 odeint_rtol=1e-4, odeint_atol=1e-5, device=None):
        super(DiffeqSolver, self).__init__()
        self.ode_func = ode_func
        self.method = method
        self.latent_dim = latent_dim
        self.odeint_rtol = odeint_rtol
        self.odeint_atol = odeint_atol
        self.device = device

    def forward(self, first_point, time_steps_to_predict, backwards=False):
        """
        Decode the trajectory through ODE Solver
        """
        n_traj_samples, n_traj = first_point.size()[0], first_point.size()[1]
        n_dims = first_point.size()[-1]

        pred_y = odeint(self.ode_func, first_point, time_steps_to_predict, 
                       rtol=self.odeint_rtol, atol=self.odeint_atol, method=self.method)
        pred_y = pred_y.permute(1, 2, 0, 3)

        assert(torch.mean(pred_y[:, :, 0, :] - first_point) < 0.001)
        assert(pred_y.size()[0] == n_traj_samples)
        assert(pred_y.size()[1] == n_traj)

        return pred_y
