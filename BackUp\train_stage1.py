import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ['TF_XLA_FLAGS'] = '--tf_xla_enable_xla_devices'
os.environ['TF_CPP_MIN_LOG_LEVEL']='2'
import argparse
import random
import warnings
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
from typing import List, Tuple
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import shutil

# --- 1. Dependencies ---
try:
    from data_loader import load_data
    from layers.Autoformer_EncDec import series_decomp
    from layers.Embed import DataEmbedding_wo_pos
    from layers.StandardNorm import Normalize
    from models.TimeMixer import PastDecomposableMixing
    # ✨ MODIFICATION: Import the visualization function from the metrics module
    from metrics.visualization_metrics import visualization
except ImportError as e:
    print(f"Import Error: {e}")
    exit()

# --- 2. Global Settings & Config ---
class Config:
    file_path = 'data/stock_data.csv'; seq_len = 24; label_len = 0; pred_len = 0; batch_size = 32
    # Stage 1 (Pre-training P+AE) settings
    stage1_epochs = 200; stage1_lr = 0.0001
    
    display_epoch_interval = 1
    
    timemixer_params = {
        'd_model': 512, 'e_layers': 2, 'd_ff': 2048, 'moving_avg': 25, 'embed': 'timeF', 'freq': 'h',
        'dropout': 0.1, 'down_sampling_window': 2, 'down_sampling_layers': 2,
        'channel_independence': False, 'decomp_method': 'moving_avg', 'top_k': 5,
    }
    def update_dims(self, num_features):
        self.enc_in = num_features; self.c_out = num_features
        self.timemixer_params.update({'enc_in': num_features, 'c_out': num_features, 'seq_len': self.seq_len, 'pred_len': self.pred_len})
cfg = Config()

# --- 3. Core Component Definitions ---
# (The local visualization function has been removed)
class MultiScaleSeasonMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleSeasonMixing, self).__init__(); seq_len, dsw, dsl = config.seq_len, config.timemixer_params['down_sampling_window'], config.timemixer_params['down_sampling_layers']; self.down_sampling_layers = nn.ModuleList([nn.Sequential(nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** (i + 1))), nn.GELU(), nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** (i + 1))),) for i in range(dsl)])
    def forward(self, season_list_permuted):
        out_high = season_list_permuted[0]; out_low = season_list_permuted[1]; out_season_list = [out_high]
        for i in range(len(season_list_permuted) - 1):
            out_low_res = self.down_sampling_layers[i](out_high); out_low = out_low + out_low_res; out_high = out_low
            if i + 2 <= len(season_list_permuted) - 1: out_low = season_list_permuted[i + 2]
            out_season_list.append(out_high)
        return out_season_list
class MultiScaleTrendMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleTrendMixing, self).__init__(); seq_len, dsw, dsl = config.seq_len, config.timemixer_params['down_sampling_window'], config.timemixer_params['down_sampling_layers']; self.up_sampling_layers = nn.ModuleList([nn.Sequential(nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** i)), nn.GELU(), nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** i)),) for i in reversed(range(dsl))])
    def forward(self, trend_list_permuted):
        trend_list_reverse = trend_list_permuted.copy(); trend_list_reverse.reverse(); out_low = trend_list_reverse[0]; out_high = trend_list_reverse[1]; out_trend_list = [out_low]
        for i in range(len(trend_list_reverse) - 1):
            out_high_res = self.up_sampling_layers[i](out_low); out_high = out_high + out_high_res; out_low = out_high
            if i + 2 <= len(trend_list_reverse) - 1: out_high = trend_list_reverse[i + 2]
            out_trend_list.append(out_low)
        out_trend_list.reverse(); return out_trend_list
class DecompositionProcessor(nn.Module):
    def __init__(self, config: Config):
        super(DecompositionProcessor, self).__init__(); self.down_sampling_window = config.timemixer_params['down_sampling_window']; self.down_sampling_layers = config.timemixer_params['down_sampling_layers']; self.down_pool = nn.AvgPool1d(kernel_size=self.down_sampling_window); self.decomposition = series_decomp(config.timemixer_params['moving_avg'])
    def forward(self, x_enc: torch.Tensor) -> Tuple[List[torch.Tensor], List[torch.Tensor]]:
        x_enc_transposed = x_enc.permute(0, 2, 1); multi_scale_list = [x_enc]; current_x = x_enc_transposed
        for _ in range(self.down_sampling_layers): current_x = self.down_pool(current_x); multi_scale_list.append(current_x.permute(0, 2, 1))
        season_list, trend_list = [], []
        for series in multi_scale_list: season, trend = self.decomposition(series); season_list.append(season); trend_list.append(trend)
        return season_list, trend_list
class MixingAutoencoder(nn.Module):
    def __init__(self, config: Config):
        super(MixingAutoencoder, self).__init__(); self.season_mixer = MultiScaleSeasonMixing(config); self.trend_mixer = MultiScaleTrendMixing(config)
    def forward(self, season_list: List[torch.Tensor], trend_list: List[torch.Tensor]) -> torch.Tensor:
        season_list_permuted = [s.permute(0, 2, 1) for s in season_list]; trend_list_permuted = [t.permute(0, 2, 1) for t in trend_list]
        mixed_seasons_permuted = self.season_mixer(season_list_permuted); mixed_trends_permuted = self.trend_mixer(trend_list_permuted)
        reconstructed_permuted = mixed_seasons_permuted[0] + mixed_trends_permuted[0]; reconstructed = reconstructed_permuted.permute(0, 2, 1)
        return reconstructed

# --- 4. Training Function for Stage 1 ---
def stage1_pretrain_autoencoder(config: Config, data_loader, device):
    """Stage 1: Pre-trains the DecompositionProcessor and the MixingAutoencoder."""
    processor = DecompositionProcessor(config).to(device)
    autoencoder = MixingAutoencoder(config).to(device)
    optimizer = optim.Adam(list(processor.parameters()) + list(autoencoder.parameters()), lr=config.stage1_lr)
    criterion = nn.MSELoss()
    
    print("--- Starting Stage 1: Pre-training Processor and Autoencoder ---")
    for epoch in range(config.stage1_epochs):
        autoencoder.train(); processor.train(); total_loss = 0
        for i, (batch_x, _, _, _) in enumerate(data_loader):
            batch_x = batch_x.float().to(device); optimizer.zero_grad()
            season_list, trend_list = processor(batch_x)
            reconstructed_output = autoencoder(season_list, trend_list)
            loss = criterion(reconstructed_output, batch_x)
            loss.backward(); optimizer.step(); total_loss += loss.item()
        print(f"[Stage 1, Epoch {epoch+1}/{config.stage1_epochs}] Reconstruction Loss: {total_loss/len(data_loader):.6f}")

    print("\n--- Generating plots for Stage 1... ---")
    autoencoder.eval(); processor.eval()
    ori_data_list = []; recon_data_list = []
    with torch.no_grad():
        # Iterate through the loader to get all data
        for batch_x, _, _, _ in data_loader:
            batch_x_cpu = batch_x.float()
            batch_x = batch_x_cpu.to(device)
            ori_data_list.append(batch_x_cpu)
            season_list, trend_list = processor(batch_x)
            reconstructed_output = autoencoder(season_list, trend_list)
            recon_data_list.append(reconstructed_output.cpu())
            
    ori_data_full = torch.cat(ori_data_list, dim=0).numpy()
    recon_data_full = torch.cat(recon_data_list, dim=0).numpy()
    
    # Call the imported visualization function
    visualization(ori_data=ori_data_full, generated_data=recon_data_full, analysis="tsne", outputs_dir="gan_outputs", file_name="stage1_reconstruction")
    visualization(ori_data=ori_data_full, generated_data=recon_data_full, analysis="pca", outputs_dir="gan_outputs", file_name="stage1_reconstruction")
    
    torch.save(processor.state_dict(), 'pretrained_models/processor.pth')
    torch.save(autoencoder.state_dict(), 'pretrained_models/mixing_autoencoder.pth')
    print("--- Stage 1 complete. Processor and Autoencoder models saved. ---")

# --- 5. Main Execution Block ---
if __name__ == '__main__':
    # Setup
    random.seed(8760); np.random.seed(8760); torch.manual_seed(8760); torch.cuda.manual_seed(8760)
    warnings.filterwarnings('ignore'); device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    print(f"--- Running on Device: {device} ---")
    
    # ✨ MODIFICATION: Updated to match the new return signature of load_data
    data_loader, num_features = load_data(
        file_path=cfg.file_path, seq_len=cfg.seq_len, label_len=cfg.label_len,
        pred_len=cfg.pred_len, batch_size=cfg.batch_size)
    
    cfg.update_dims(num_features)
    print(f"Data loaded. Number of features: {num_features}")

    print("Clearing directories for a fresh run...")
    for dir_path in ['gan_outputs', 'pretrained_models']:
        if os.path.exists(dir_path): shutil.rmtree(dir_path)
        os.makedirs(dir_path)
        
    # Execute Stage 1
    # ✨ MODIFICATION: Pass the correctly named data_loader
    stage1_pretrain_autoencoder(cfg, data_loader, device)
