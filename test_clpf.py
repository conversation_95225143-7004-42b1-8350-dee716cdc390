#!/usr/bin/env python3
"""
Test script for CLPF generator
"""

import torch
import torch.nn as nn
from clpf_generator import NewCLPFGenerator

class TestConfig:
    def __init__(self):
        self.seq_len = 24
        self.latent_dim = 128
        self.timemixer_params = {
            'd_model': 768, 
            'd_ff': 3072, 
            'dropout': 0.1,
            'down_sampling_window': 2, 
            'down_sampling_layers': 3, 
            'moving_avg': 9,
            'enc_in': 5,  # number of features
            'c_out': 5
        }

def test_clpf_generator():
    print("Testing CLPF Generator...")
    
    # Create test configuration
    config = TestConfig()
    device = torch.device("cpu")  # Use CPU for testing
    
    try:
        # Create CLPF generator
        generator = NewCLPFGenerator(config.latent_dim, config, device)
        print("✅ CLPF Generator created successfully")
        
        # Test forward pass
        batch_size = 4
        z = torch.randn(batch_size, config.latent_dim)
        
        seasonal_list, trend_list = generator(z)
        
        print(f"✅ Forward pass successful")
        print(f"   Seasonal components: {len(seasonal_list)} scales")
        print(f"   Trend components: {len(trend_list)} scales")
        
        for i, (s, t) in enumerate(zip(seasonal_list, trend_list)):
            print(f"   Scale {i}: seasonal {s.shape}, trend {t.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_clpf_generator()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Tests failed!")
