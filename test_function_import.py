#!/usr/bin/env python3
"""
测试函数导入是否正常
"""

def test_function_imports():
    """测试函数导入"""
    print("🔍 测试函数导入...")
    
    try:
        # 测试导入train2.py中的函数
        from train2 import (
            test_generator_stage1,
            calculate_reconstruction_errors,
            create_generator_comparison_plot
        )
        
        print("✅ 所有函数导入成功！")
        print(f"  - test_generator_stage1: {test_generator_stage1}")
        print(f"  - calculate_reconstruction_errors: {calculate_reconstruction_errors}")
        print(f"  - create_generator_comparison_plot: {create_generator_comparison_plot}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_function_signatures():
    """测试函数签名"""
    print("\n🔍 测试函数签名...")
    
    try:
        from train2 import (
            test_generator_stage1,
            calculate_reconstruction_errors,
            create_generator_comparison_plot
        )
        import inspect
        
        # 检查test_generator_stage1的签名
        sig1 = inspect.signature(test_generator_stage1)
        print(f"  - test_generator_stage1{sig1}")
        
        # 检查calculate_reconstruction_errors的签名
        sig2 = inspect.signature(calculate_reconstruction_errors)
        print(f"  - calculate_reconstruction_errors{sig2}")
        
        # 检查create_generator_comparison_plot的签名
        sig3 = inspect.signature(create_generator_comparison_plot)
        print(f"  - create_generator_comparison_plot{sig3}")
        
        print("✅ 所有函数签名正常！")
        return True
        
    except Exception as e:
        print(f"❌ 函数签名检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 函数导入测试")
    print("=" * 50)
    
    tests = [
        ("函数导入测试", test_function_imports),
        ("函数签名测试", test_function_signatures),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"⚠️  {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！函数定义和导入正常！")
        print("\n现在可以正常运行 train2.py 了！")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败")
    
    return passed == total

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
