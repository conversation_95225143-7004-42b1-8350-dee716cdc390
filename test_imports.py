#!/usr/bin/env python3
"""
Test script for imports
"""

def test_imports():
    print("Testing imports...")
    
    try:
        import clpf_lib
        print("✅ clpf_lib imported successfully")
    except Exception as e:
        print(f"❌ clpf_lib import failed: {e}")
        return False
    
    try:
        import clpf_tools
        print("✅ clpf_tools imported successfully")
    except Exception as e:
        print(f"❌ clpf_tools import failed: {e}")
        return False
    
    try:
        import clpf_train_misc
        print("✅ clpf_train_misc imported successfully")
    except Exception as e:
        print(f"❌ clpf_train_misc import failed: {e}")
        return False
    
    try:
        import clpf_ode_rnn_encoder
        print("✅ clpf_ode_rnn_encoder imported successfully")
    except Exception as e:
        print(f"❌ clpf_ode_rnn_encoder import failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 All imports successful!")
    else:
        print("\n💥 Some imports failed!")
