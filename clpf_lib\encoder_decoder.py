# Simplified encoder decoder for CLPF
import torch
import torch.nn as nn
import clpf_lib.utils as utils


class Encoder_z0_ODE_RNN(nn.Module):
    def __init__(self, latent_dim, input_dim, z0_diffeq_solver, z0_dim, 
                 n_gru_units=100, device=torch.device("cpu")):
        super(Encoder_z0_ODE_RNN, self).__init__()
        
        self.latent_dim = latent_dim
        self.input_dim = input_dim
        self.device = device
        self.z0_dim = z0_dim
        self.z0_diffeq_solver = z0_diffeq_solver
        
        self.transform_z0 = nn.Sequential(
            nn.Linear(latent_dim * 2, n_gru_units),
            nn.Tanh(),
            nn.Linear(n_gru_units, z0_dim * 2),
        )
        utils.init_network_weights(self.transform_z0)

    def forward(self, data, time_steps, run_backwards=True, save_info=False):
        # data, time_steps -- observations and their time stamps
        # IMPORTANT: assumes that 'data' already has mask concatenated to it
        assert not torch.isnan(data).any()
        assert not torch.isnan(time_steps).any()

        n_traj, n_tp, n_dims = data.size()
        if len(time_steps) == 1:
            prev_y = torch.zeros((1, n_traj, self.latent_dim)).to(self.device)
            prev_std = torch.zeros((1, n_traj, self.latent_dim)).to(self.device)

            xi = data[:, 0, :].unsqueeze(0)

            last_yi, last_yi_std = self.GRU_update(prev_y, prev_std, xi)
            extra_info = None
        else:
            last_yi, last_yi_std, _, extra_info = self.run_odernn(
                data, time_steps, run_backwards=run_backwards, save_info=save_info
            )

        means_z0 = last_yi.reshape(1, n_traj, self.latent_dim)
        std_z0 = last_yi_std.reshape(1, n_traj, self.latent_dim)

        mean_z0, std_z0 = utils.split_last_dim(self.transform_z0(torch.cat((means_z0, std_z0), -1)))
        std_z0 = std_z0.abs()
        if save_info:
            self.extra_info = extra_info

        return mean_z0, std_z0

    def GRU_update(self, prev_y, prev_std, xi):
        # Simple GRU update - simplified version
        return prev_y, prev_std

    def run_odernn(self, data, time_steps, run_backwards=True, save_info=False):
        # Simplified ODE RNN - just return some dummy values for now
        n_traj, n_tp, n_dims = data.size()
        last_yi = torch.zeros((1, n_traj, self.latent_dim)).to(self.device)
        last_yi_std = torch.zeros((1, n_traj, self.latent_dim)).to(self.device)
        return last_yi, last_yi_std, None, None
