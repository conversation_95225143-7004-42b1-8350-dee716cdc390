#!/usr/bin/env python3
"""
快速测试脚本：验证CLPF模型的基本导入和功能
"""

import torch
import sys

def quick_import_test():
    """快速导入测试"""
    print("🔍 快速导入测试...")
    
    try:
        # 测试基础导入
        print("  - 测试 clpf_lib...")
        from clpf_lib import layers
        
        print("  - 测试 clpf_generator...")
        from clpf_generator import NewCLPFGenerator
        
        print("  - 测试 train2 组件...")
        from train2 import Config, DecompositionalEncoder
        
        print("✅ 所有导入成功！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_generator_test():
    """快速生成器测试"""
    print("\n🔍 快速生成器测试...")
    
    try:
        from clpf_generator import NewCLPFGenerator
        from train2 import Config
        
        # 创建配置
        cfg = Config()
        device = torch.device("cpu")  # 使用CPU避免GPU问题
        
        # 创建生成器
        print("  - 创建CLPF生成器...")
        generator = NewCLPFGenerator(cfg.latent_dim, cfg, device)
        
        # 测试前向传播
        print("  - 测试前向传播...")
        batch_size = 2
        z = torch.randn(batch_size, cfg.latent_dim)
        
        with torch.no_grad():
            seasonal_list, trend_list = generator(z)
        
        print(f"  - 输出: {len(seasonal_list)} 季节性组件, {len(trend_list)} 趋势组件")
        print("✅ 生成器测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def quick_train_syntax_test():
    """快速训练语法测试"""
    print("\n🔍 快速训练语法测试...")
    
    try:
        # 只测试能否导入训练函数，不实际运行
        from train2 import (
            stage1_train_generators, 
            stage2_train_discriminators,
            stage3_joint_train,
            test_model
        )
        
        print("✅ 训练函数导入成功！")
        return True
        
    except Exception as e:
        print(f"❌ 训练函数导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 CLPF模型快速测试")
    print("=" * 40)
    
    tests = [
        quick_import_test,
        quick_generator_test,
        quick_train_syntax_test
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        else:
            break  # 如果有测试失败，停止后续测试
    
    print("\n" + "=" * 40)
    if passed == len(tests):
        print("🎉 快速测试全部通过！")
        print("你可以运行完整测试: python test_train2.py")
        print("或者直接训练: python train2.py")
    else:
        print(f"⚠️  测试失败 ({passed}/{len(tests)})")
        print("请检查错误信息并修复问题")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
