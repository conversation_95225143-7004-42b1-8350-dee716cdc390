# Simplified ODE function for CLPF
import torch
import torch.nn as nn
import clpf_lib.utils as utils


class ODEFunc(nn.Module):
    def __init__(self, input_dim, latent_dim, ode_func_net, device=torch.device("cpu")):
        super(ODEFunc, self).__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.device = device
        
        self.gradient_net = ode_func_net

    def forward(self, t, y, backwards=False):
        """
        Perform one step in solving ODE. Given data point y and time point t, 
        return the gradient dy/dt at this time point
        
        t: current time point
        y: value at the current time point
        """
        grad = self.get_ode_gradient_nn(t, y)
        if backwards:
            grad = -grad
        return grad

    def get_ode_gradient_nn(self, t, y):
        return self.gradient_net(y)

    def sample_next_point_from_prior(self, t, y):
        """
        Sample next point from the prior distribution
        """
        return torch.zeros_like(y).normal_(std=0.1)
