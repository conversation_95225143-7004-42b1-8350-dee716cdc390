# train_stage2.py
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ['TF_XLA_FLAGS'] = '--tf_xla_enable_xla_devices'
os.environ['TF_CPP_MIN_LOG_LEVEL']='2'

import random
import warnings
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
from typing import List, Tuple
import matplotlib.pyplot as plt
from torch.nn.utils import spectral_norm

# --- 1. Dependencies ---
try:
    from data_loader import load_data
    from layers.Autoformer_EncDec import series_decomp
    from torchdiffeq import odeint
    # Assuming you have created these separate metric files
    from metrics.visualization_metrics import visualization 
    from metrics.quantitative_metrics import discriminative_score_metrics, predictive_score_metrics
except ImportError as e:
    print(f"Import Error: {e}")
    exit()

# --- 2. Global Settings & Config ---
class Config:
    file_path = 'data/stock_data.csv'; seq_len = 24; label_len = 0; pred_len = 1; batch_size = 32
    
    epochs = 150 # Using a small number for quick testing
    lr_g = 0.0001; lr_d = 0.0001; betas = (0.5, 0.9)
    lambda_l1 = 25.0; latent_dim = 128
    
    lambda_gp = 15.0
    critic_iterations = 5
    metric_iteration = 10 

    processor_path = 'pretrained_models/processor.pth'
    autoencoder_path = 'pretrained_models/mixing_autoencoder.pth'
    output_dir = 'gan_outputs'
    
    timemixer_params = {'down_sampling_window': 2, 'down_sampling_layers': 2, 'moving_avg': 25}
    
    pcf_disc_hidden_dim = 256
    pcf_disc_lstm_layers = 2
    
    def update_dims(self, num_features):
        self.enc_in = num_features
        self.c_out = num_features
cfg = Config()

# --- 3. Helper & Core Component Definitions ---
# (All helper classes like compute_gradient_penalty, DecompositionProcessor, ODEFunc,
#  CLPFGenerator, PCFDiscriminator, etc. remain here, unchanged)
class DecompositionProcessor(nn.Module):
    def __init__(self, config: Config):
        super(DecompositionProcessor, self).__init__(); self.down_sampling_window = config.timemixer_params['down_sampling_window']; self.down_sampling_layers = config.timemixer_params['down_sampling_layers']; self.down_pool = nn.AvgPool1d(kernel_size=self.down_sampling_window); self.decomposition = series_decomp(config.timemixer_params['moving_avg'])
    def forward(self, x_enc: torch.Tensor) -> Tuple[List[torch.Tensor], List[torch.Tensor]]:
        x_enc_transposed = x_enc.permute(0, 2, 1); multi_scale_list = [x_enc]; current_x = x_enc_transposed
        for _ in range(self.down_sampling_layers): current_x = self.down_pool(current_x); multi_scale_list.append(current_x.permute(0, 2, 1))
        season_list, trend_list = [], []
        for series in multi_scale_list: season, trend = self.decomposition(series); season_list.append(season); trend_list.append(trend)
        return season_list, trend_list
class ODEFunc(nn.Module):
    def __init__(self, data_dim: int):
        super(ODEFunc, self).__init__()
        self.net = nn.Sequential(
            nn.Linear(data_dim, 512), nn.Tanh(), 
            nn.Linear(512, 1024), nn.Tanh(), 
            nn.Linear(1024, 512), nn.Tanh(), 
            nn.Linear(512, data_dim)
        )
    def forward(self, t, x): return self.net(x)
class MultiScaleSeasonMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleSeasonMixing, self).__init__(); seq_len, dsw, dsl = config.seq_len, config.timemixer_params['down_sampling_window'], config.timemixer_params['down_sampling_layers']; self.down_sampling_layers = nn.ModuleList([nn.Sequential(nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** (i + 1))), nn.GELU(), nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** (i + 1))),) for i in range(dsl)])
    def forward(self, season_list_permuted):
        out_high = season_list_permuted[0]; out_low = season_list_permuted[1]; out_season_list = [out_high]
        for i in range(len(season_list_permuted) - 1):
            out_low_res = self.down_sampling_layers[i](out_high); out_low = out_low + out_low_res; out_high = out_low
            if i + 2 <= len(season_list_permuted) - 1: out_low = season_list_permuted[i + 2]
            out_season_list.append(out_high)
        return out_season_list
class MultiScaleTrendMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleTrendMixing, self).__init__(); seq_len, dsw, dsl = config.seq_len, config.timemixer_params['down_sampling_window'], config.timemixer_params['down_sampling_layers']; self.up_sampling_layers = nn.ModuleList([nn.Sequential(nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** i)), nn.GELU(), nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** i)),) for i in reversed(range(dsl))])
    def forward(self, trend_list_permuted):
        trend_list_reverse = trend_list_permuted.copy(); trend_list_reverse.reverse(); out_low = trend_list_reverse[0]; out_high = trend_list_reverse[1]; out_trend_list = [out_low]
        for i in range(len(trend_list_reverse) - 1):
            out_high_res = self.up_sampling_layers[i](out_low); out_high = out_high + out_high_res; out_low = out_high
            if i + 2 <= len(trend_list_reverse) - 1: out_high = trend_list_reverse[i + 2]
            out_trend_list.append(out_low)
        out_trend_list.reverse(); return out_trend_list
class MixingAutoencoder(nn.Module):
    def __init__(self, config: Config):
        super(MixingAutoencoder, self).__init__(); self.season_mixer = MultiScaleSeasonMixing(config); self.trend_mixer = MultiScaleTrendMixing(config)
    def forward(self, season_list: List[torch.Tensor], trend_list: List[torch.Tensor]) -> torch.Tensor:
        season_list_permuted = [s.permute(0, 2, 1) for s in season_list]; trend_list_permuted = [t.permute(0, 2, 1) for t in trend_list]
        mixed_seasons_permuted = self.season_mixer(season_list_permuted); mixed_trends_permuted = self.trend_mixer(trend_list_permuted)
        reconstructed_permuted = mixed_seasons_permuted[0] + mixed_trends_permuted[0]; reconstructed = reconstructed_permuted.permute(0, 2, 1)
        return reconstructed
class CLPFGenerator(nn.Module):
    def __init__(self, latent_dim: int, config: Config):
        super(CLPFGenerator, self).__init__()
        self.config = config; self.output_dim = 0
        for i in range(config.timemixer_params['down_sampling_layers'] + 1):
            seq_len_i = config.seq_len // (config.timemixer_params['down_sampling_window'] ** i)
            self.output_dim += seq_len_i * config.c_out
        self.ode_func = ODEFunc(self.output_dim)
        self.z_mapper = nn.Linear(latent_dim, self.output_dim)
        self.integration_time = torch.tensor([0, 1]).float()
    def forward(self, z: torch.Tensor) -> List[torch.Tensor]:
        batch_size = z.size(0); x0 = self.z_mapper(z); t = self.integration_time.to(z.device)
        states = odeint(self.ode_func, x0, t, method='dopri5', rtol=1e-4, atol=1e-5)
        final_state = states[1]; component_list = []; current_pos = 0
        for i in range(self.config.timemixer_params['down_sampling_layers'] + 1):
            seq_len_i = self.config.seq_len // (self.config.timemixer_params['down_sampling_window'] ** i)
            component_size = seq_len_i * self.config.c_out
            component = final_state[:, current_pos : current_pos + component_size]
            component_list.append(component.view(batch_size, seq_len_i, self.config.c_out))
            current_pos += component_size
        return component_list
class LSTMBasedDiscriminator(nn.Module):
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int):
        super().__init__()
        self.rnn = nn.LSTM(input_size=input_dim, hidden_size=hidden_dim, num_layers=num_layers, batch_first=True)
        self.output_dim = hidden_dim
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        lstm_out, (h_n, _) = self.rnn(x); last_hidden_state = h_n[-1, :, :]; return last_hidden_state
class PCFDiscriminator(nn.Module):
    def __init__(self, config: Config):
        super(PCFDiscriminator, self).__init__()
        self.scale_discriminators = nn.ModuleList()
        final_mlp_input_dim = 0
        num_scales = config.timemixer_params['down_sampling_layers'] + 1
        for _ in range(num_scales):
            scale_d = LSTMBasedDiscriminator(
                input_dim=config.enc_in,
                hidden_dim=config.pcf_disc_hidden_dim,
                num_layers=config.pcf_disc_lstm_layers
            )
            self.scale_discriminators.append(scale_d)
            final_mlp_input_dim += scale_d.output_dim
        self.final_mlp = nn.Sequential(
            spectral_norm(nn.Linear(final_mlp_input_dim, 256)),
            nn.LeakyReLU(0.2),
            spectral_norm(nn.Linear(256, 1))
        )
    def forward(self, series_list: List[torch.Tensor]) -> torch.Tensor:
        scale_features = []
        for i, series in enumerate(series_list):
            features = self.scale_discriminators[i](series)
            scale_features.append(features)
        combined_features = torch.cat(scale_features, dim=1)
        return self.final_mlp(combined_features)
def compute_gradient_penalty(discriminator, real_samples, fake_samples, device):
    real_flat = torch.cat([s.reshape(s.size(0), -1) for s in real_samples], dim=1)
    fake_flat = torch.cat([s.reshape(s.size(0), -1) for s in fake_samples], dim=1)
    alpha = torch.randn(real_flat.size(0), 1, device=device)
    interpolates_flat = (alpha * real_flat + ((1 - alpha) * fake_flat)).requires_grad_(True)
    interp_list = []
    current_pos = 0
    for real_s in real_samples:
        size = real_s.shape[1] * real_s.shape[2]
        interp_list.append(interpolates_flat[:, current_pos:current_pos+size].view_as(real_s))
        current_pos += size
    with torch.backends.cudnn.flags(enabled=False):
        d_interpolates = discriminator(interp_list)
    grad_outputs = torch.ones(d_interpolates.size(), device=device, requires_grad=False)
    gradients = torch.autograd.grad(
        outputs=d_interpolates, inputs=interpolates_flat,
        grad_outputs=grad_outputs, create_graph=True, retain_graph=True, only_inputs=True
    )[0]
    gradients = gradients.view(gradients.size(0), -1)
    return ((gradients.norm(2, dim=1) - 1) ** 2).mean()

# --- 5. Training and Testing Functions ---

def train_generator_hybrid(config: Config, data_loader, device):
    try:
        processor = DecompositionProcessor(config).to(device)
        processor.load_state_dict(torch.load(config.processor_path, map_location=device))
        processor.eval()
        for param in processor.parameters(): param.requires_grad = False
        print("Pre-trained Processor loaded and frozen.")
    except FileNotFoundError:
        print(f"Error: {config.processor_path} not found."); return

    g_season = CLPFGenerator(config.latent_dim, config).to(device)
    g_trend = CLPFGenerator(config.latent_dim, config).to(device)
    d_season = PCFDiscriminator(config).to(device)
    d_trend = PCFDiscriminator(config).to(device)
    
    optimizer_G = optim.Adam(list(g_season.parameters()) + list(g_trend.parameters()), lr=config.lr_g, betas=config.betas)
    optimizer_D = optim.Adam(list(d_season.parameters()) + list(d_trend.parameters()), lr=config.lr_d, betas=config.betas)
    criterion_l1 = nn.L1Loss()

    scheduler_G = optim.lr_scheduler.StepLR(optimizer_G, step_size=100, gamma=0.75)
    scheduler_D = optim.lr_scheduler.StepLR(optimizer_D, step_size=100, gamma=0.75)

    print("--- Starting Stage 2: Hybrid Training ---")
    for epoch in range(config.epochs):
        for i, (batch_x, _, _, _) in enumerate(data_loader):
            batch_x = batch_x.float().to(device)
            b_size = batch_x.size(0)
            
            # --- Train Discriminators ---
            optimizer_D.zero_grad()
            with torch.no_grad():
                real_season, real_trend = processor(batch_x)
                z_season = torch.randn(b_size, config.latent_dim, device=device)
                z_trend = torch.randn(b_size, config.latent_dim, device=device)
                fake_season = [s.detach() for s in g_season(z_season)]
                fake_trend = [t.detach() for t in g_trend(z_trend)]
            
            loss_d_season = d_season(fake_season).mean() - d_season(real_season).mean()
            loss_d_trend = d_trend(fake_trend).mean() - d_trend(real_trend).mean()
            gp_season = compute_gradient_penalty(d_season, real_season, fake_season, device)
            gp_trend = compute_gradient_penalty(d_trend, real_trend, fake_trend, device)
            loss_d = loss_d_season + loss_d_trend + config.lambda_gp * (gp_season + gp_trend)
            loss_d.backward()
            optimizer_D.step()

            # --- Train Generators ---
            if (i + 1) % config.critic_iterations == 0:
                optimizer_G.zero_grad()
                z_season = torch.randn(b_size, config.latent_dim, device=device)
                z_trend = torch.randn(b_size, config.latent_dim, device=device)
                new_fake_season = g_season(z_season)
                new_fake_trend = g_trend(z_trend)
                
                loss_g_adv = -d_season(new_fake_season).mean() - d_trend(new_fake_trend).mean()
                
                loss_g_l1 = 0
                with torch.no_grad():
                    real_s_target, real_t_target = processor(batch_x)
                for j in range(len(real_s_target)):
                    loss_g_l1 += criterion_l1(new_fake_season[j], real_s_target[j])
                    loss_g_l1 += criterion_l1(new_fake_trend[j], real_t_target[j])
                
                loss_g_total = loss_g_adv + config.lambda_l1 * loss_g_l1
                loss_g_total.backward()
                optimizer_G.step()
        
        if 'loss_g_total' in locals() and 'loss_d' in locals():
            print(f"[Stage 2, Epoch {epoch+1}/{config.epochs}] D Loss: {loss_d.item():.4f}, G Loss: {loss_g_total.item():.4f}")
        elif 'loss_d' in locals():
            print(f"[Stage 2, Epoch {epoch+1}/{config.epochs}] D Loss: {loss_d.item():.4f}, G Loss: N/A")

        scheduler_G.step()
        scheduler_D.step()

    torch.save(g_season.state_dict(), 'pretrained_models/generator_season.pth')
    torch.save(g_trend.state_dict(), 'pretrained_models/generator_trend.pth')
    print("--- Stage 2 training complete. Generator models saved. ---")


def test_model(config: Config, data_loader, device):
    """
    Loads trained models, generates a full synthetic dataset, 
    calculates quantitative metrics, and performs visualization.
    """
    print("\n--- Starting Final Evaluation and Visualization ---")

    # 1. Load trained models
    g_season = CLPFGenerator(config.latent_dim, config).to(device)
    g_trend = CLPFGenerator(config.latent_dim, config).to(device)
    autoencoder = MixingAutoencoder(config).to(device)

    try:
        g_season.load_state_dict(torch.load('pretrained_models/generator_season.pth', map_location=device))
        g_trend.load_state_dict(torch.load('pretrained_models/generator_trend.pth', map_location=device))
        autoencoder.load_state_dict(torch.load(config.autoencoder_path, map_location=device))
    except FileNotFoundError as e:
        print(f"Error loading models: {e}. Cannot perform testing.")
        return

    g_season.eval()
    g_trend.eval()
    autoencoder.eval()
    print("All necessary models loaded for testing.")

    # 2. Collect all original data and generate synthetic data
    ori_data_list_for_metric = []
    for batch_x, _, _, _ in data_loader:
        ori_data_list_for_metric.extend(batch_x.numpy())
        
    ori_data_full_for_viz = np.array(ori_data_list_for_metric)
    synth_size = len(ori_data_full_for_viz)
    print(f"Original data collected. Total samples: {synth_size}")

    generated_data_list = []
    with torch.no_grad():
        for i in range(0, synth_size, config.batch_size):
            b_size = min(config.batch_size, synth_size - i)
            if b_size == 0: continue

            z_season = torch.randn(b_size, config.latent_dim, device=device)
            z_trend = torch.randn(b_size, config.latent_dim, device=device)
            
            fake_s_batch = g_season(z_season)
            fake_t_batch = g_trend(z_trend)
            fake_recon = autoencoder(fake_s_batch, fake_t_batch)
            generated_data_list.append(fake_recon.cpu().numpy())

    generated_data_full_for_viz = np.concatenate(generated_data_list, axis=0)
    gen_data_list_for_metric = [generated_data_full_for_viz[i] for i in range(len(generated_data_full_for_viz))]
    print(f"Synthetic data generated. Total samples: {len(generated_data_full_for_viz)}")

    # 3. Performance metrics calculation
    metric_results = dict()

    # Discriminative Score
    discriminative_score = list()
    print('Start discriminative_score_metrics')
    for i in range(config.metric_iteration):
        print('discriminative_score iteration: ', i)
        temp_disc = discriminative_score_metrics(ori_data_list_for_metric, gen_data_list_for_metric)
        discriminative_score.append(temp_disc)
    metric_results['discriminative'] = np.mean(discriminative_score)
    print('Finish discriminative_score_metrics compute')

    # Predictive score
    predictive_score = list()
    print('Start predictive_score_metrics')
    for i in range(config.metric_iteration):
        print('predictive_score iteration: ', i)
        temp_predict = predictive_score_metrics(ori_data_list_for_metric, gen_data_list_for_metric)
        predictive_score.append(temp_predict)
    metric_results['predictive'] = np.mean(predictive_score)
    print('Finish predictive_score_metrics compute')
    
    # 4. Visualization
    print("Generating PCA and t-SNE plots...")
    visualization(ori_data_full_for_viz, generated_data_full_for_viz, 'pca', config.output_dir,"stage2_Hybrid")
    visualization(ori_data_full_for_viz, generated_data_full_for_viz, 'tsne', config.output_dir,"stage2_Hybrid")
    print(f"Plots saved to '{config.output_dir}' directory.")

    # 5. Print final metric scores
    print("\n--- Quantitative Metric Results ---")
    print(metric_results)


# --- 6. Main Execution Block ---
if __name__ == '__main__':
    random.seed(8760); np.random.seed(8760); torch.manual_seed(8760); torch.cuda.manual_seed_all(8760)
    warnings.filterwarnings('ignore'); device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    os.makedirs(cfg.output_dir, exist_ok=True)
    os.makedirs('pretrained_models', exist_ok=True)
    
    print(f"--- Running on Device: {device} ---")
    
    # ✨ MODIFICATION: Updated to match the new return signature of load_data
    data_loader, num_features = load_data(
        file_path=cfg.file_path, seq_len=cfg.seq_len, label_len=cfg.label_len,
        pred_len=cfg.pred_len, batch_size=cfg.batch_size)
    
    cfg.update_dims(num_features)
    print(f"Data loaded. Number of features: {num_features}")
    
    # 1. Train the model
    train_generator_hybrid(cfg, data_loader, device)
    
    # 2. Test the trained model and visualize the results
    test_model(cfg, data_loader, device)
